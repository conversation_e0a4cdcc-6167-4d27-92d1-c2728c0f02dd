import { memo, ReactNode, TouchEvent, useCallback, useRef, useState } from 'react';
import ReactLoading from 'react-loading';
import { useNavigate } from 'react-router-dom';
import { HeaderProps, useCommonHeader } from '../Header/Header';
import './styles.scss';

/**
 * PullDownToRefreshProps
 */
export interface PullDownToRefreshProps {
    /**
     * children
     */
    children: ReactNode;
    /**
     * useHeader
     */
    useHeader?: boolean | HeaderProps;
}

const REFRESH_THRESHOLD = 20;

/**
 * PullDownToRefresh
 * @param param0
 * @returns
 */
const PullDownToRefresh = ({ children, useHeader }: PullDownToRefreshProps): JSX.Element => {
    const { height } = useCommonHeader(Boolean(useHeader));
    const [pulling, setPulling] = useState(false);
    const [distance, setDistance] = useState(0);
    const startY = useRef<undefined | number>(undefined);
    const navigate = useNavigate();

    /**
     * handleTouchStart
     * @param event TouchEvent<HTMLDivElement>
     */
    const handleTouchStart = useCallback((event: TouchEvent<HTMLDivElement>) => {
        if (window.scrollY === 0) {
            startY.current = event.touches[0].clientY;
        }
    }, []);

    /**
     * handleTouchMove
     * @param event TouchEvent<HTMLDivElement>
     */
    const handleTouchMove = useCallback((event: TouchEvent<HTMLDivElement>) => {
        if (startY.current) {
            const currentY = event.touches[0].clientY;
            const diff = currentY - (startY.current ?? 0);
            if (diff > 0) {
                setPulling(true);
                setDistance(diff);
            }
        }
    }, []);

    /**
     * handleTouchEnd
     */
    const handleTouchEnd = useCallback(() => {
        if (pulling && distance > REFRESH_THRESHOLD) {
            navigate(0);
        }
        setPulling(false);
        setDistance(0);
        startY.current = undefined;
    }, [distance, pulling, navigate]);

    return (
        <div
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            className="pull-down-to-refresh-container"
        >
            {pulling && (
                <div
                    className="spinner-container"
                    style={{
                        // eslint-disable-next-line no-mixed-operators
                        transform: `scale(${0.5 + 0.5 * Math.min(distance / REFRESH_THRESHOLD, 1)})`,
                        marginTop: 10 + (height ?? 0),
                    }}
                >
                    <ReactLoading type={'spokes'} color={'#000'} />
                </div>
            )}
            {children}
        </div>
    );
};

export default memo(PullDownToRefresh);
