const API = {
    STATUS_CODE: {
        ERROR: 1,
        SUCCESS: 0,
        OUT_OF_STOCK: 6,
    },
    HTTP_STATUS_CODE: {
        ERROR: 500,
        SUCCESS: 200,
    },
    // 100-002
    AREA_WALLET_CARD: '/area/wallet/card',
    // 100-005
    AREA_WALLET_PRODUCTLIST: '/area/wallet/productlist',
    CHEER_LOGIN_PICTURE: '/cheer/login/picture',
    // 100-007
    AREA_WALLET_PAYMENT_CONFIG_GET: '/area/wallet/payment/config/get',
    // 100-004
    AREA_WALLET_CASH_RECEIVE_REAL: '/area/wallet/cash/receive/real',
    // POP_VERITRANS_URL
    POP_VERITRANS_URL: 'https://pay3.veritrans.co.jp/pop/v1/javascripts/pop.js',
    CHEER_REGISTER_AUTH: '/cheer/register/auth',
    CHEER_REGISTER_ACCOUNT: '/cheer/register/account',
    // 000-004
    AREA_OCR: '/area/ocr',
    // 000-010
    AREA_WALLET_COUPON: '/area/wallet/coupon',

    // entry002-init-get-brandinfo
    ENTRY002_INIT_GET_BRANDINFO: '/entry002-init-get-brandinfo',
    // entry002-main-send-authcode
    ENTRY002_MAIN_SEND_AUTHCODE: '/entry002-main-send-authcode',
    // entry003-main-send-authcode
    ENTRY003_MAIN_SEND_AUTHCODE: '/entry003-main-send-authcode',
    // entry003-main-check-authcode
    ENTRY003_MAIN_CHECK_AUTHCODE: '/entry003-main-check-authcode',
    // entry006-main-get-token
    ENTRY006_MAIN_GET_TOKEN: '/entry006-main-get-token',
    // entry007-main-get-token
    ENTRY007_MAIN_GET_TOKEN: '/entry007-main-get-token',
    // entry006-main-register-account
    ENTRY006_MAIN_REGISTER_ACCOUNT: '/entry006-main-register-account',
    // entry007-main-register-account
    ENTRY007_MAIN_REGISTER_ACCOUNT: '/entry007-main-register-account',
    // news001-init-get-personal-news-list
    NEWS001_INIT_GET_PERSONAL_NEWS_LIST: '/news001-init-get-personal-news-list',
    // news001-init-get-important-news-list
    NEWS001_INIT_GET_IMPORTANT_NEWS_LIST: '/news001-init-get-important-news-list',
    // news001-init-get-important-news-unread-flag
    NEWS001_INIT_GET_IMPORTANT_NEWS_UNREAD_FLAG: '/news001-init-get-important-news-unread-flag',
    // news001-init--get-personal-news-unread-flag
    NEWS001_INIT_GET_PERSONAL_NEWS_UNREAD_FLAG: '/news001-init-get-personal-news-unread-flag',
    // news002-init-get-news-detail
    NEWS002_INIT_GET_NEWS_DETAIL: '/news002-init-get-news-detail',
    // contact001-main-send-inquiry
    CONTACT001_MAIN_SEND_INQUIRY: '/contact001-main-send-inquiry',
    // contact001-init-get-email
    CONTACT001_INIT_GET_EMAIL: '/contact001-init-get-email',
    // login001-main-get-token
    LOGIN001_MAIN_GET_TOKEN: '/login001-main-get-token',
    // login001-main-get-line-token
    LOGIN001_MAIN_GET_LINE_TOKEN: '/login001-main-get-line-token',
    // login000-main-get-entryenableflag
    LOGIN000_MAIN_GET_ENTRYENABLE_FLAG: '/login000-main-get-entryenableflag',
    // login001-main-get-loginenableflag
    LOGIN001_MAIN_GET_LOGIN_ENABLE_FLAG: '/login001-main-get-loginenableflag',
    // home001-init-get-news-unread-latest
    HOME001_INIT_GET_NEWS_UNREAD_LATEST: '/home001-init-get-news-unread-latest',
    // home001-init-get-coin
    HOME001_INIT_GET_COIN: '/home001-init-get-coin',
    // home001-init-get-banner-list
    HOME001_INIT_GET_BANNER_LIST: '/home001-init-get-banner-list',
    // home001-init-get-coupon-list
    HOME001_INIT_GET_COUPON_LIST: '/home001-init-get-coupon-list',
    // home001-init-get-event-ticket-list
    HOME001_INIT_GET_EVENT_TICKET_LIST: '/home001-init-get-event-ticket-list',
    // home001-init-get-news-unread-flag
    HOME001_INIT_GET_NEWS_UNREAD_FLAG: '/home001-init-get-news-unread-flag',
    // home001-init-get-userinfo
    HOME001_INIT_GET_USERINFO: '/home001-init-get-userinfo',
    // login000-init-get-keyvisual-list
    LOGIN000_INIT_GET_KEYVISUAL_LIST: '/login000-init-get-keyvisual-list',
    // login001-init-get-keyvisual-list
    LOGIN001_INIT_GET_KEYVISUAL_LIST: '/login001-init-get-keyvisual-list',
    // login001-init-get-banner-list
    LOGIN001_INIT_GET_BANNER_LIST: '/login001-init-get-banner-list',
    // coupon001/init/get/coupon/list
    COUPON001_INIT_GET_COUPON_LIST: '/coupon001-init-get-coupon-list',
    // coupon003/init/get/coupon/detail
    COUPON003_INIT_GET_COUPON_DETAIL: '/coupon003-init-get-coupon-detail',
    // coupon004/main/use/coupon
    COUPON004_MAIN_USE_COUPON: '/coupon004-main-use-coupon',
    // settingpass001-main-check-authcode
    SETTINGPASS001_MAIN_CHECK_AUTH_CODE: '/settingpass001-main-check-authcode',
    // settingpass001-main-send-authcode
    SETTINGPASS001_MAIN_SEND_AUTH_CODE: '/settingpass001-main-send-authcode',
    // settingpass002-main-update-password
    SETTINGPASS002_MAIN_UPDATE_PASSWORD: '/settingpass002-main-update-password',
    // settingtel001-main-send-authcode
    SETTINGTEL001_MAIN_SEND_AUTHCODE: '/settingtel001-main-send-authcode',
    // settingtel001-main-check-authcode
    SETTINGTEL001_MAIN_CHECK_AUTHCODE: '/settingtel001-main-check-authcode',
    // settingtel002-main-send-authcode
    SETTINGTEL002_MAIN_SEND_AUTHCODE: '/settingtel002-main-send-authcode',
    // settingtel003-main-check-authcode
    SETTINGTEL003_MAIN_CHECK_AUTHCODE: '/settingtel003-main-check-authcode',
    // settingtel003-main-send-authcode
    SETTINGTEL003_MAIN_SEND_AUTHCODE: '/settingtel003-main-send-authcode',
    // settingtel003-main-update-tel
    SETTINGTEL003_MAIN_UPDATE_TEL: '/settingtel003-main-update-tel',
    // settingtop001-init-get-userinfo
    SETTINGTOP001_INIT_GET_USERINFO: '/settingtop001-init-get-userinfo',
    // settingtop001-main-send-authcode
    SETTINGTOP001_MAIN_SEND_AUTHCODE: '/settingtop001-main-send-authcode',
    // settingmail001-main-send-authcode
    SETTINGMAIL001_MAIN_SEND_AUTHCODE: '/settingmail001-main-send-authcode',
    // settingmail001-main-check-authcode
    SETTINGMAIL001_MAIN_CHECK_AUTHCODE: '/settingmail001-main-check-authcode',
    // settingmail002-main-send-authcode
    SETTINGMAIL002_MAIN_SEND_AUTHCODE: '/settingmail002-main-send-authcode',
    // settingmail003-main-send-authcode
    SETTINGMAIL003_MAIN_SEND_AUTHCODE: '/settingmail003-main-send-authcode',
    // settingmail003-main-check-authcode
    SETTINGMAIL003_MAIN_CHECK_AUTHCODE: '/settingmail003-main-check-authcode',
    // settingmail003-main-update-email
    SETTINGMAIL003_MAIN_UPDATE_EMAIL: '/settingmail003-main-update-email',
    // reset001/main/send/authcode
    RESET001_MAIN_SEND_AUTHCODE: '/reset001/main/send/authcode',
    // reset002/main/check/authcode
    RESET002_MAIN_CHECK_AUTHCODE: '/reset002/main/check/authcode',
    // reset002/main/send/authcode
    RESET002_MAIN_SEND_AUTHCODE: '/reset002/main/send/authcode',
    // reset005/main/reset/password
    RESET_MAIN_RESET_PASSWORD_005: '/reset005/main/reset/password',
    // settingmailmagazine001-main-update-mailmagazine
    SETTINGMAILMAGAZINE001_MAIN_UPDATE_MAILMAGAZINE: '/settingmailmagazine001-main-update-mailmagazine',
    // settingtop001-main-update-nickname
    SETTINGTOP001_MAIN_UPDATE_NICKNAME: '/settingtop001-main-update-nickname',
    // reset005/main/reset/password
    RESET005_MAIN_RESET_PASSWORD: '/reset005/main/reset/password',
    // deleteaccount001/main/send/authcode
    DELETEACCOUNT001_MAIN_SEND_AUTHCODE: '/deleteaccount001/main/send/authcode',
    // deleteaccount001/main/check/authcode
    DELETEACCOUNT001_MAIN_CHECK_AUTHCODE: '/deleteaccount001/main/check/authcode',
    // deleteaccount002/main/delete/account
    DELETEACCOUNT002_MAIN_DELETE_ACCOUNT: '/deleteaccount002/main/delete/account',
    // store001/init/get/store/query
    STORE001_INIT_GET_STORE_QUERY: '/store001/init/get/store/query',
    // store001/main/get/store/query
    STORE001_MAIN_GET_STORE_QUERY: '/store001/main/get/store/query',
    // store003/init/store/detail
    STORE003_INIT_GET_STORE_DETAIL: '/store003/init/get/store/detail',
    // chargecredit001-init-get-chargelimit
    CHARGE_CREDIT_001_INIT_GET_CHARGE_LIMIT: '/chargecredit001-init-get-chargelimit',
    // chargecredit002-main-execute-charge
    CHARGE_CREDIT_002_MAIN_EXECUTE_CHARGE: '/chargecredit002-main-execute-charge',
    // chargecnveni001-init-get-chargelimit
    CHARGE_CNVENI_001_INIT_GET_CHARGE_LIMIT: '/chargecnveni001-init-get-chargelimit',
    // chargecnveni002-main-execute-charge
    CHARGE_CNVENI_002_MAIN_EXECUTE_CHARGE: '/chargecnveni002-main-execute-charge',
    // chargeintbnk001-init-get-chargelimit
    CHARGE_INTBNK_001_INIT_GET_CHARGE_LIMIT: '/chargeintbnk001-init-get-chargelimit',
    // chargeintbnkpaytest001-main-register-bankpay
    CHARGE_INTBNK_PAY_001_MAIN_REGISTER_BANKPAY: '/chargeintbnkpaytest001-main-register-bankpay',
    // chargeintbnkpaytest001-init-get-bankinfo
    CHARGE_INTBNK_PAY_001_INIT_GET_BANKINFO: '/chargeintbnkpaytest001-init-get-bankinfo',
    // chargeintbnkpaytest001-main-register-bankaccount
    CHARGE_INTBNK_PAY_001_MAIN_REGISTER_BANKACCOUNT: '/chargeintbnkpaytest001-main-register-bankaccount',
    // chargeintbnkpaytest001-main-execute-charge
    CHARGE_INTBNK_PAY_001_MAIN_EXECUTE_CHARGE: '/chargeintbnkpaytest001-main-execute-charge',
    // chargeintbnkpaytest001-main-delete-bankaccount
    CHARGE_INTBNK_PAY_001_MAIN_DELETE_BANKACCOUNT: '/chargeintbnkpaytest001-main-delete-bankaccount',
    // chargeintbnk002-main-execute-charge
    CHARGE_INTBNK_002_MAIN_EXECUTE_CHARGE: '/chargeintbnk002-main-execute-charge',
    // paymulti001/main/search/storepubliccode
    PAYMULTI001_MAIN_SEARCH_STOREPUBLICCODE: '/paymulti001/main/search/storepubliccode',
    // paymulti002/main/search/storepubliccode
    PAYMULTI002_MAIN_SEARCH_STOREPUBLICCODE: '/paymulti002/main/search/storepubliccode',
    // paymulti003/init/get/availablecoinid
    PAYMULTI003_INIT_GET_AVAILABLECOINID: '/paymulti003/init/get/availablecoinid',
    // paymulti005/main/execute/payment
    PAYMULTI005_MAIN_EXECUTE_PAYMENT: '/paymulti005/main/execute/payment',
    // chargeprepid001-main-execute-charge
    CHARGE_PREPID_001_MAIN_EXECUTE_CHARGE: '/chargeprepid001-main-execute-charge',
    // chargeprepid001-main-execute-ocr
    CHARGE_PREPID_001_MAIN_EXECUTE_OCR: '/chargeprepid001-main-execute-ocr',
    // paysingle001/main/search/storepubliccode
    PAY_SINGLE001_MAIN_SEARCH_STORE_PUBLIC_CODE: '/paysingle001/main/search/storepubliccode',
    // paysingle002/main/search/storepubliccode
    PAY_SINGLE002_MAIN_SEARCH_STORE_PUBLIC_CODE: '/paysingle002/main/search/storepubliccode',
    // paysingle004/main/execute/payment
    PAY_SINGLE004_MAIN_EXECUTE_PAYMENT: '/paysingle004/main/execute/payment',
    // history001/init/get/history/list
    HISTORY001_INIT_GET_HISTORY_LIST: '/history001/init/get/history/list',
    // rocketssurvey001/init/get/survey/list
    ROCKETS_SURVEY001_INIT_GET_SURVEY_LIST: '/rocketssurvey001/init/get/survey/list',
    // rocketssurvey002/main/send/answer
    ROCKETS_SURVEY002_MAIN_SEND_ANSWER: '/rocketssurvey002/main/send/answer',
    // survey001-init-get-survey-list
    SURVEY001_INIT_GET_SURVEY_LIST: '/survey001-init-get-survey-list',
    // survey002/init/get/survey/list
    SURVEY002_INIT_GET_SURVEY_LIST: '/survey002-init-get-survey-list',
    // survey003/main/send/answer
    SURVEY003_MAIN_SEND_ANSWER: '/survey003-main-send-answer',
    // settingpersonal001-main-send-authcode
    SETTING_PERSONAL001_MAIN_SEND_AUTHCODE: '/settingpersonal001-main-send-authcode',
    // settingpersonal001-main-check-authcode
    SETTING_PERSONAL001_MAIN_CHECK_AUTHCODE: '/settingpersonal001-main-check-authcode',
    // settingmail001-main-send-authcode
    SETTING_PERSONAL002_INIT_GET_PERSONALINFO: '/settingpersonal002-init-get-personalinfo',
    // settingpersonal003-main-update-address
    SETTING_PERSONAL003_MAIN_UPDATE_ADDRESS: '/settingpersonal003-main-update-address',
    // settingtop001-main-unlink-line
    SETTINGTOP001_MAIN_UNLINK_LINE: '/settingtop001-main-unlink-line',
    // settingtop001-main-get-line-token
    SETTINGTOP001_MAIN_GET_LINE_TOKEN: '/settingtop001-main-get-line-token',
    // common/refresh/token
    COMMON_REFRESH_TOKEN: '/common/refresh/token',
    // chargevouchr002-main-execute-charge
    CHARGEVOUCHR002_MAIN_EXECUTE_CHARGE: '/chargevouchr002-main-execute-charge',
    // chargevouchr002-init-get-clientkey
    CHARGEVOUCHR002_INIT_GET_CLIENT_KEY: '/chargevouchr002/init/get/clientkey',
    // chargeselect001-init-get-clientkey
    CHARGESELECT001_INIT_GET_CLIENT_KEY: '/chargeselect001/init/get/clientkey',
    // event001-init-get-event-ticket-list
    EVENT001_INIT_GET_EVENT_TICKET_LIST: '/event001-init-get-event-ticket-list',
    // event001-init-get-evevnt-ticket-query
    EVENT001_INIT_GET_EVENT_TICKET_QUERY: '/event001-init-get-event-ticket-query',
    // event001-init-get-evevnt-ticket-detail
    EVENT003_INIT_GET_EVENT_TICKET_DETAIL: '/event003-init-get-event-ticket-detail',
    // event003-init-get-availablecoinid
    EVENT003_INIT_GET_AVAILABLECOINID: '/event003-init-get-availablecoinid',
    // event005-main-wallet-cash-transfer
    EVENT005_MAIN_WALLET_CASH_TRANSFER: '/event005/main/wallet/cash/transfer',
    // ticket001-init-get-event-ticket-list
    TICKET001_INIT_GET_EVENT_TICKET_LIST: 'ticket001/init/get/event/ticket/list',
    // ticket002-init-get-event-ticket-detail
    TICKET002_INIT_GET_EVENT_TICKET_DETAIL: '/ticket002/init/get/event/ticket/detail',
    // ticket003/main/use/event/ticket
    TICKET003_MAIN_USE_EVENT_TICKET: '/ticket003/main/use/event/ticket',
    // stamprally005-init-update-stamprally
    STAMPRALLY005_INIT_UPDATE_STAMPRALLY: '/stamprally005-init-update-stamprally',
    // stamprally006-main-search-stamprally
    STAMPRALLY006_MAIN_SEARCH_STAMPRALLY: '/stamprally006-main-search-stamprally',
    // stamprally004-init-get-stamprally-mission-list
    STAMPRALLY004_INIT_GET_STAMPRALLY_MISSION_LIST: '/stamprally004-init-get-stamprally-mission-list',
    // stamprally001-init-get-stamprally-list
    STAMPRALLY001_INIT_GET_STAMPRALLY_LIST: '/stamprally001-init-get-stamprally-list',
    // stamprally002-init-get-stamprally-detail
    STAMPRALLY002_INIT_GET_STAMPRALLY_LIST_DETAIL: '/stamprally002-init-get-stamprally-detail',
    // stamprally003-init-get-stamprally-item
    STAMPRALLY003_INIT_GET_STAMPRALLY_LIST_ITEM: '/stamprally003-init-get-stamprally-item',
    // chargeqrcodepresent001-init-create-transfercode
    CHARGEQRCODEPRESENT001_INIT_CREATE_TRANSFERCODE: '/chargeqrcodepresent001-init-create-transfercode',
    // eventform002-init-get-eventform-detail
    EVENTFORM002_INIT_GET_EVENTFORM_DETAIL: '/eventform002-init-get-eventform-detail',
    // chargeqrcoderead001-main-execute-charge
    CHARGE_QR_CODE_READ001_MAIN_EXECUTE_CHARGE: '/chargeqrcoderead001-main-execute-charge',
    // chargeqrcoderead002-main-execute-charge
    CHARGE_QR_CODE_READ002_MAIN_EXECUTE_CHARGE: '/chargeqrcoderead002-main-execute-charge',
    // eventform001-init-get-eventform-listm
    EVENTFORM001_INIT_GET_EVENTFORM_LIST: '/eventform001-init-get-eventform-list',
    // eventform002-main-send-answer
    EVENTFORM002_MAIN_SEND_ANSWER: '/eventform002-main-send-answer',
    // api write history
    COMMON_WRITE_TRANSITION_HISTORY: '/common/write/history',
    // api user transition history
    COMMON_USER_TRANSITION_HISTORY: '/common/user/transition/history',
    // nft001-init-get-nft-category-list
    NFT001_INIT_GET_NFT_CATEGORY_LIST: '/nft001-init-get-nft-category-list',
    // nft002-init-get-nft-category-detail
    NFT002_INIT_GET_NFT_CATEGORY_DETAIL: '/nft002-init-get-nft-category-detail',
    // nft002-main-send-nft-user-like
    NFT002_MAIN_SEND_NFT_USER_LIKE: '/nft002-main-send-nft-user-like',
    // nft002-init-get-nft-purchase-list
    NFT002_INIT_GET_NFT_PURCHASE_LIST: '/nft002-init-get-nft-purchase-list',
    // nft003-init-get-nft-detail
    NFT003_INIT_GET_NFT_DETAIL: '/nft003-init-get-nft-detail',
    // nft003-main-send-nft-user-like
    NFT003_MAIN_SEND_NFT_USER_LIKE: '/nft003/main/send/nft/user/like',
    // nft003-main-send-nft-user-like
    NFT005_INIT_CHECK_NFT_QR: '/nft005/init/check/nft/qr',
    // nft008-init-get-nft
    NFT008_INIT_GET_NFT: '/nft008/init/get/nft',
    // nft008-init-get-coin
    NFT008_INIT_GET_COIN: '/nft008/init/get/coin',
    // nft008-main-check-nft-serial
    NFT008_MAIN_CHECK_NFT_SERIAL: '/nft008/main/check/nft/serial',
    // nft009-main-execute-payment
    NFT009_MAIN_EXECUTE_PAYMENT: '/nft009/main/execute/payment',
    // nft006-init-check-nft-qr
    NFT006_INIT_CHECK_NFT_QR: '/nft006/init/check/nft/qr',
    // nft007-init-get-nft
    NFT007_INIT_GET_NFT: '/nft007/init/get/nft',
    // nftcollection001-init-get-nft-user-list
    NFTCOLLECTION001_INIT_GET_NFT_USER_LIST: '/nftcollection001/init/get/nft/user/list',
    // nftcollection002-init-get-nft-user
    NFTCOLLECTION002_INIT_GET_NFT_USER: '/nftcollection002/init/get/nft/user',
    // mission001-init-get-mission-list
    MISSION001_INIT_GET_MISSION_LIST: '/mission001-init-get-mission-list',
    // mission002-init-get-mission-detail
    MISSION002_INIT_GET_MISSION_DETAIL: '/mission002-init-get-mission-detail',
    // mission003-init-get-mission-detail
    MISSION003_INIT_GET_MISSION_DETAIL: '/mission003/init/get/mission/detail',
    // mission003-main-update-mission
    MISSION003_MAIN_UPDATE_MISSION: '/mission003-main-update-mission',
    // common-ec-get-cartexists
    COMMON_EC_GET_CARTEXISTS: '/common-ec-get-cartexists',
    // common-ec-get-url
    COMMON_EC_GET_URL: '/common-ec-get-url',
    // ecitem001-ec-get-itemlist
    ECITEM001_EC_GET_ITEMLIST: '/ecitem001-ec-get-itemlist',
    // ecitem002-ec-get-item
    ECITEM002_EC_GET_ITEM: '/ecitem002-ec-get-item',
    // ecitem002-ec-update-cart
    ECITEM002_EC_UPDATE_CART: '/ecitem002-ec-update-cart',
    // common-ec-update-cart
    COMMON_EC_UPDATE_CART: '/common-ec-update-cart',
    // ecpayment001-ec-check-beforepayment
    ECPAYMENT001_EC_CHECK_BEFOREPAYMENT: '/ecpayment001-ec-check-beforepayment',
    // ecpayment001-ec-get-updatedatetime
    ECPAYMENT001_EC_GET_UPDATEDATETIME: '/ecpayment001-ec-get-updatedatetime',
    // area-nft-agreement-version-get
    AREA_NFT_AGREEMENT_VERSION_GET: '/area/nft/agreement/version/get',
    // area-nft-agreement-version-update
    AREA_NFT_AGREEMENT_VERSION_UPDATE: '/area/nft/agreement/version/update',
    // common-ec-update-paymentexpired
    COMMON_EC_UPDATE_PAYMENTEXPIRED: '/common-ec-update-paymentexpired',
    // eccart001-ec-get-cart
    ECCART001_EC_GET_CART: '/eccart001-ec-get-cart',
    // eccart001-ec-delete-item
    ECCART001_EC_DELETE_ITEM: '/eccart001-ec-delete-item',
    // ecpurchase001-ec-get-purchaseprocess
    ECPURCHASE001_EC_GET_PURCHASEPROCESS: '/ecpurchase001-ec-get-purchaseprocess',
    // ecpurchase001-ec-update-purchaseprocess
    ECPURCHASE001_EC_UPDATE_PURCHASEPROCESS: '/ecpurchase001-ec-update-purchaseprocess',
    // ecpurchase002-ec-get-purchasehistorylist
    ECPURCHASE002_EC_GET_PURCHASEHISTORYLIST: '/ecpurchase002-ec-get-purchasehistorylist',
    // common-wallet-get-payment
    COMMON_WALLET_GET_PAYMENT: '/common/wallet/get/payment',
    // common-ec-update-updatedatetime
    COMMON_EC_UPDATE_UPDATEDATETIME: '/common/ec/update/updatedatetime',
    // menu001-main-revoke-token
    MENU001_MAIN_REVOKE_TOKEN: '/menu001/main/revoke/token',
    // item001-init-get-itemlist
    ITEM001_INIT_GET_ITEMLIST: '/item001/init/get/itemlist',
    // item002-init-get-itemquery
    ITEM002_INIT_GET_ITEMQUERY: '/item002/init/get/itemquery',

    // DAO apis
    // daovoting001-dao-get-votingthemelist
    DAOVOTING001_DAO_GET_VOTINGTHEMELIST: '/daovoting001-dao-get-votingthemelist',
    // common-dao-get-functionauthority
    COMMON_DAO_GET_FUNCTIONAUTHORITY: '/common-dao-get-functionauthority',
    // common-dao-get-participantstatus
    COMMON_DAO_GET_PARTICIPANTSTATUS: '/common-dao-get-participantstatus',
    // daovoting004-dao-get-votingtheme
    DAO_VOTING004_DAO_GET_VOTINGTHEME: '/daovoting004-dao-get-votingtheme',
    // daovoting004-dao-get-votingdefaultinfo
    DAO_VOTING004_DAO_GET_VOTINGDEFAULTINFO: '/daovoting004-dao-get-votingdefaultinfo',
    // daovoting004-dao-put-votingtheme
    DAO_VOTING004_DAO_PUT_VOTINGTHEME: '/daovoting004-dao-put-votingtheme',
    // daovoting004-dao-update-votingtheme
    DAO_VOTING004_DAO_UPDATE_VOTINGTHEME: '/daovoting004-dao-update-votingtheme',
    // daovoting004-dao-delete-votingtheme
    DAO_VOTING004_DAO_DELETE_VOTINGTHEME: '/daovoting004-dao-delete-votingtheme',
    // daovoting002-dao-get-votinginfo
    DAOVOTING002_DAO_GET_VOTINGINFO: '/daovoting002-dao-get-votinginfo',
    // daovoting002-dao-get-votingright
    DAOVOTING002_DAO_GET_VOTINGRIGHT: '/daovoting002-dao-get-votingright',
    // daovoting002-dao-get-votingstatusinfo
    DAOVOTING002_DAO_GET_VOTINGSTATUSINFO: '/daovoting002-dao-get-votingstatusinfo',
    // daovoting002-dao-get-votingcheerinfo
    DAOVOTING002_DAO_GET_VOTINGCHEERINFO: '/daovoting002-dao-get-votingcheerinfo',
    // daovoting002-dao-update-voting
    DAOVOTING002_DAO_UPDATE_VOTING: '/daovoting002-dao-update-voting',
    // daocommunity004-dao-update-category
    DAO_COMMUNITY004_DAO_UPDATE_CATEGORY: '/daocommunity004-dao-update-category',
    // daocommunity004-dao-delete-category
    DAO_COMMUNITY004_DAO_DELETE_CATEGORY: '/daocommunity004-dao-delete-category',
    // daocommunity005-dao-put-channel
    DAO_COMMUNITY005_DAO_PUT_CHANNEL: '/daocommunity005-dao-put-channel',
    // daocommunity006-dao-update-channel
    DAO_COMMUNITY006_DAO_UPDATE_CHANNEL: '/daocommunity006-dao-update-channel',
    // daocommunity006-dao-delete-channel
    DAO_COMMUNITY006_DAO_DELETE_CHANNEL: '/daocommunity006-dao-delete-channel',
    // daocommunity003-dao-put-category
    DAO_COMMUNITY003_DAO_PUT_CATEGORY: '/daocommunity003-dao-put-category',
    // daocommunity002-dao-get-categorychannel
    DAOCOMMUNITY002_DAO_GET_CATEGORY_CHANNEL: '/daocommunity002-dao-get-categorychannel',
    // daocommunity002-dao-get-mentionlist
    DAOCOMMUNITY002_DAO_GET_MENTION_LIST: '/daocommunity002-dao-get-mentionlist',
    // daocommunity002-dao-get-noreadcount
    DAOCOMMUNITY002_DAO_GET_NO_READ_COUNT: '/daocommunity002-dao-get-noreadcount',
    // daocommunity008-dao-get-thread
    DAO_COMMUNITY008_DAO_GET_THREAD: '/daocommunity008-dao-get-thread',
    // daocommunity008-dao-get-chat
    DAO_COMMUNITY008_DAO_GET_CHAT: '/daocommunity008-dao-get-chat',
    // common-dao-get-url
    COMMON_DAO_GET_URL: '/common-dao-get-url',
    // daocommunity008-dao-get-reactionimage
    DAO_COMMUNITY008_DAO_GET_REACT_IMAGE: '/daocommunity008-dao-get-reactionimage',
    // daocommunity008-dao-get-chatread
    DAO_COMMUNITY008_DAO_GET_CHAT_READ: '/daocommunity008-dao-get-chatread',
    // daocommunity008-dao-update-chatread
    DAO_COMMUNITY008_DAO_UPDATE_CHAT_READ: '/daocommunity008-dao-update-chatread',
    // daocommunity007-dao-put-chat
    DAO_COMMUNITY007_DAO_PUT_CHAT: '/daocommunity007-dao-put-chat',
    // daocommunity008-dao-update-chat
    DAO_COMMUNITY008_DAO_UPDATE_CHAT: '/daocommunity008-dao-update-chat',
    // daocommunity008-dao-delete-chat
    DAO_COMMUNITY008_DAO_DELETE_CHAT: '/daocommunity008-dao-delete-chat',
    // daocommunity008-dao-delete-reaction
    DAO_COMMUNITY008_DAO_DELETE_REACTION: '/daocommunity008-dao-delete-reaction',
    // daocommunity008-dao-put-reaction
    DAO_COMMUNITY008_DAO_PUT_REACTION: '/daocommunity008-dao-put-reaction',
    // daocommunity008-dao-get-participant
    DAO_COMMUNITY008_DAO_GET_PARTICIPANT: '/daocommunity008-dao-get-participant',
    // daotop001-dao-get-participantstatus
    DAOTOP001_DAO_GET_PARTICIPANTSTATUS: '/daotop001-dao-get-participantstatus',
    // daotop001-dao-get-hotchat
    DAOTOP001_DAO_GET_HOTCHAT: '/daotop001-dao-get-hotchat',
    // daocommunity008-dao-get-reactionimage
    DAOCOMMUNITY008_DAO_GET_REACTIONIMAGE: '/daocommunity008-dao-get-reactionimage',
    // daotop002-dao-get-participant
    DAOTOP002_DAO_GET_PARTICIPANT: '/daotop002-dao-get-participant',
    // daotop002-dao-PUT-participant
    DAOTOP002_DAO_PUT_PARTICIPANT: '/daotop002-dao-put-participant',
    // daotop003-dao-get-profile
    DAOTOP003_DAO_GET_PROFILE: '/daotop003-dao-get-profile',
    // daotop003-dao-exit-participant
    DAOTOP003_DAO_EXIT_PARTICIPANT: '/daotop003-dao-exit-participant',
    // daotop004-dao-update-profile
    DAOTOP004_DAO_UPDATE_PROFILE: '/daotop004-dao-update-profile',
    // common-mobileorder-get-url
    COMMON_MOBILE_ORDER_GET_URL: '/common-mobileorder-get-url',
    // common-mobileorder-get-cartexists
    COMMON_MOBILEORDER_GET_CARTEXISTS: '/common-mobileorder-get-cartexists',
    // mobileorderitem001-mobileorder-get-storename
    MOBILEORDERITEM001_MOBILEORDER_GET_STORENAME: '/mobileorderitem001-mobileorder-get-storename',
    // mobileorderitem001-mobileorder-get-itemlist
    MOBILEORDERITEM001_MOBILEORDER_GET_ITEMLIST: '/mobileorderitem001-mobileorder-get-itemlist',
    // mobileorderitem002-mobileorder-get-item
    MOBILEORDERITEM002_MOBILEORDER_GET_ITEM: '/mobileorderitem002-mobileorder-get-item',
    // common-mobileorder-update-cart
    COMMON_MOBILEORDER_UPDATE_CART: '/common-mobileorder-update-cart',
    // commonheader001-init-get-news-unread-flag
    COMMONHEADER001_INIT_GET_NEWS_UNREAD_FLAG: '/commonheader001-init-get-news-unread-flag',
    // wallet001-main-get-payableflag
    WALLET001_MAIN_GET_PAYABLEFLAG: '/wallet001-main-get-payableflag',
    // wallet001-main-get-chargeableflag
    WALLET001_MAIN_GET_CHARGEABLEFLAG: '/wallet001-main-get-chargeableflag',
    // wallet001-init-get-recommendation-popup-detail
    WALLET001_INIT_GET_RECOMMENDATION_POPUP_DETAIL: '/wallet001-init-get-recommendation-popup-detail',
    // wallet001-init-get-userinfo
    WALLET001_INIT_GET_USERINFO: '/wallet001-init-get-userinfo',
    // wallet001-init-get-news-unread-latest
    WALLET001_INIT_GET_NEWS_UNREAD_LATEST: '/wallet001-init-get-news-unread-latest',
    // wallet001-init-get-coin
    WALLET001_INIT_GET_COIN: '/wallet001-init-get-coin',
    // wallet001-init-get-user-ticket-list
    WALLET001_INIT_GET_USER_TICKET_LIST: '/wallet001-init-get-user-ticket-list',
    // wallet001-init-get-coupon-list
    WALLET001_INIT_GET_COUPON_LIST: '/wallet001-init-get-coupon-list',
    // wallet001-init-get-limited-store-list
    WALLET001_INIT_GET_LIMITED_STORE_LIST: '/wallet001-init-get-limited-store-list',
    // wallet001-init-get-banner-list
    WALLET001_INIT_GET_BANNER_LIST: '/wallet001-init-get-banner-list',
    // store001-main-get-store-query
    STORE001_MAIN_GET_STORE_QUERY_2: '/store001-main-get-store-query',
    // mobileordercart001-mobileorder-get-cart
    MOBILEORDERCARD001_MOBILEORDER_GET_CART: '/mobileordercart001-mobileorder-get-cart',
    // mobileordercart001-mobileorder-delete-item
    MOBILEORDERCART001_MOBILEORDER_DELETE_ITEM: '/mobileordercart001-mobileorder-delete-item',
    // mobileordercart001-mobileorder-check-beforeconfirm
    MOBILEORDERCART001_MOBILEORDER_CHECK_BEFORECONFIRM: '/mobileordercart001-mobileorder-check-beforeconfirm',
    // mobileordercart001-mobileorder-confirm-order
    MOBILEORDERCART001_MOBILEORDER_CONFIRM_ORDER: '/mobileordercart001-mobileorder-confirm-order',
    // top001-init-get-info
    TOP001_INIT_GET_INFO: '/top001-init-get-info',
    // top001-init-get-storelist
    TOP001_INIT_GET_STORELIST: '/top001-init-get-storelist',
    // top001-init-get-eventlist
    TOP001_INIT_GET_EVENTLIST: '/top001-init-get-eventlist',
    // top001-init-get-stamprallylist
    TOP001_INIT_GET_STAMPRALLYLIST: '/top001-init-get-stamprallylist',
    // top001-init-get-missionlist
    TOP001_INIT_GET_MISSIONLIST: '/top001-init-get-missionlist',
    // top001-init-get-ecitemlist
    TOP001_INIT_GET_ECITEMLIST: '/top001-init-get-ecitemlist',
    // top001-init-get-nftcontentlist
    TOP001_INIT_GET_NFTCONTENTLIST: '/top001-init-get-nftcontentlist',
    // top001-init-get-daolist
    TOP001_INIT_GET_DAOLIST: '/top001-init-get-daolist',
    // top001-init-get-couponlist
    TOP001_INIT_GET_COUPONLIST: '/top001-init-get-couponlist',
    // mobileorderhistory001-mobileorder-get-orderlist
    MOBILEORDERHISTORY001_MOBILEORDER_GET_ORDERLIST: '/mobileorderhistory001-mobileorder-get-orderlist',
    // brand001-init-get-brand-list
    BRAND001_INIT_GET_BRAND_LIST: '/brand001-init-get-brand-list',
    // brand002-main-update-brand
    BRAND002_MAIN_UPDATE_BRAND: '/brand002-main-update-brand',
    // daotop001-dao-get-daolist
    DAOTOP001_DAO_GET_DAOLIST: '/daotop001-dao-get-daolist',
    // daotop005-dao-get-detail
    DAOTOP005_DAO_GET_DETAIL: '/daotop005-dao-get-detail',
    // common-init-get-userinfo
    COMMON_INIT_GET_USERINFO: '/common-init-get-userinfo',
    // common-init-get-clientkey
    COMMON_INIT_GET_CLIENTKEY: '/common-init-get-clientkey',
    // qrcodepresent001-init-create-transfercode
    QRCODEPRESENT001_INIT_CREATE_TRANSFERCODE: '/qrcodepresent001-init-create-transfercode',
    // qrcodepresent001-init-get-result
    QRCODEPRESENT001_INIT_GET_RESULT: '/qrcodepresent001-init-get-result',
    // chargeselect001-main-get-bankpayregisterflag
    CHARGESELECT001_MAIN_GET_BANKPAYREGISTERFLAG: '/chargeselect001-main-get-bankpayregisterflag',
    // chargeintbnkpay002-init-get-bankname
    CHARGEINTBNKPAY002_INIT_GET_BANKNAME: '/chargeintbnkpay002-init-get-bankname',
    // chargeintbnkpay002-main-register-bankaccount
    CHARGEINTBNKPAY002_MAIN_REGISTER_BANKACCOUNT: '/chargeintbnkpay002-main-register-bankaccount',
    // chargeintbnkpay005-init-get-chargelimit
    CHARGEINTBNKPAY005_INIT_GET_CHARGE_LIMIT: '/chargeintbnkpay005-init-get-chargelimit',
    // chargeintbnkpay005-init-get-bankinfo
    CHARGEINTBNKPAY005_INIT_GET_BANKINFO: '/chargeintbnkpay005-init-get-bankinfo',
    // chargeintbnkpay008-init-get-bankinfo
    CHARGEINTBNKPAY008_INIT_GET_BANKINFO: '/chargeintbnkpay008-init-get-bankinfo',
    // chargeintbnkpay008-init-get-bankinfo
    CHARGEINTBNKPAY009_INIT_GET_INFO: '/chargeintbnkpay009-init-get-info',
    // chargeintbnkpay009-main-register-bankaccount
    CHARGEINTBNKPAY009_MAIN_REGISTER_BANKACCOUNT: '/chargeintbnkpay009-main-register-bankaccount',
    // chargeintbnkpay008-main-delete-bankaccount
    CHARGEINTBNKPAY008_MAIN_DELETE_BANKACCOUNT: '/chargeintbnkpay008-main-delete-bankaccount',
    // chargeintbnkpay006-main-execute-charge
    CHARGEINTBNKPAY006_MAIN_EXECUTE_CHARGE: '/chargeintbnkpay006-main-execute-charge',
    // idea dao
    // ideadao010-main-create-task
    IDEADAO010_MAIN_CREATE_TASK: '/ideadao010-main-create-task',
    // ideadao009-init-get-task-list
    IDEADAO009_INIT_GET_TASK_LIST: '/ideadao009-init-get-task-list',
    // ideadao001-init-get-ideadao-list
    IDEADAO001_INIT_GET_IDEADAO_LIST: '/ideadao001-init-get-ideadao-list',
    // ideadao001-dao-get-contribution -> ideadao001-init-get-contribution
    IDEADAO001_DAO_GET_CONTRIBUTION: '/ideadao001-init-get-contribution',
    // ideadao003/main/create/dao
    IDEADAO003_MAIN_CREATE_DAO: '/ideadao003-main-create-dao',
    // ideadao005/main/update/ideadao
    IDEADAO005_MAIN_UPDATE_IDEADAO: '/ideadao005-main-update-ideadao',
    // ideadao007/init/get/members/list
    IDEADAO007_INIT_GET_MEMBERS_LIST: '/ideadao007-init-get-members-list',
    // ideadao008/dao/get/profile
    IDEADAO008_DAO_GET_PROFILE: '/ideadao008-dao-get-profile',
    // ideadao008/dao/update/profile
    IDEADAO008_DAO_UPDATE_PROFILE: '/ideadao008-dao-update-profile',
    // ideadao011-init-get-taskinfodetail
    IDEADAO011_INIT_GET_TASKINFODETAIL: '/ideadao011-init-get-taskinfodetail',
    // ideadao011-main-update-tasknotstarted
    IDEADAO011_MAIN_UPDATE_TASKNOTSTARTED: '/ideadao011-main-update-tasknotstarted',
    // ideadao011-main-update-taskinprogress
    IDEADAO011_MAIN_UPDATE_TASKINPROGRESS: '/ideadao011-main-update-taskinprogress',
    // ideadao011-main-update-taskcompleted
    IDEADAO011_MAIN_UPDATE_TASKCOMPLETED: '/ideadao011-main-update-taskcompleted',
    // ideadao011-main-update-taskapproved
    IDEADAO011_MAIN_UPDATE_TASKAPPROVED: '/ideadao011-main-update-taskapproved',
    // ideadao011-main-delete-taskattachments
    IDEADAO011_MAIN_DELETE_TASKATTACHMENTS: '/ideadao011-main-delete-taskattachments',
    // ideadao011-main-add-taskattachments
    IDEADAO011_MAIN_ADD_TASKATTACHMENTS: '/ideadao011-main-add-taskattachments',
    // common-get-medalinfo
    COMMON_GET_MEDALINFO: '/common-get-medalinfo',
    // common-transfer-main-execute-payment
    COMMON_TRANSFER_MAIN_EXECUTE_PAYMENT: '/common-transfer-main-execute-payment',
    // common-transfer-main-get-userinfo
    COMMON_TRANSFER_MAIN_GET_USERINFO: '/common-transfer-main-get-userinfo',
    // common-transfer-main-get-orderid
    COMMON_TRANSFER_MAIN_GET_ORDER_ID: '/common-transfer-main-get-orderid',
    // transfer007-init-get-coin
    TRANSFER007_INIT_GET_COIN: '/transfer007-init-get-coin',
    // common-mynumber-init-get-mynasetting-noauth
    COMMON_MYNUMBER_INIT_GET_MYNASETTING_NOAUTH: '/common-mynumber-init-get-mynasetting-noauth',
    // mynumber002-init-get-usercert
    MYNUMBER002_INIT_GET_USERCERT: '/mynumber002-init-get-usercert',
    // mynumber002-init-get-usercert-stub
    MYNUMBER002_INIT_GET_USERCERT_STUB: '/mynumber002-init-get-usercert-stub',
    // mynumber002-init-get-envconfigid
    MYNUMBER002_INIT_GET_ENVCONFIGID: '/mynumber002-init-get-envconfigid',
    // mynumber003-init-get-servicescreencall
    MYNUMBER003_INIT_GET_SERVICESCREENCALL: '/mynumber003-init-get-servicescreencall',
    // mynumber005-init-get-cardinfo
    MYNUMBER005_INIT_GET_CARDINFO: '/mynumber005-init-get-cardinfo',
    // mynumber006-init-get-startscreencall
    MYNUMBER006_INIT_GET_STARTSCREENCALL: '/mynumber006-init-get-startscreencall',
    // mynumber008-init-get-mcas
    MYNUMBER008_INIT_GET_MCAS: '/mynumber008-init-get-mcas',
    // common-init-get-oidc
    COMMON_INIT_GET_OIDC: '/common-init-get-oidc',
    // mynumber008-main-update-mcas
    MYNUMBER008_MAIN_UPDATE_MCAS: '/mynumber008-main-update-mcas',
    // mynumber002-init-get-usercert
    MYNUMBER002_INIT_GET_USERCERT_TEST: '/mynumber002-init-get-usercert',
    // mynumber002-init-get-usercert-stub
    MYNUMBER002_INIT_GET_USERCERT_STUB_TEST: '/mynumber002-init-get-usercert-stub',
    // mynumber002-init-get-envconfigid
    MYNUMBER002_INIT_GET_ENVCONFIGID_TEST: '/mynumber002-init-get-envconfigid',
    // mynumber003-init-get-servicescreencall
    MYNUMBER003_INIT_GET_SERVICESCREENCALL_TEST: '/mynumber003-init-get-servicescreencall',
    // mynumber005-init-get-cardinfo
    MYNUMBER005_INIT_GET_CARDINFO_TEST: '/mynumber005-init-get-cardinfo',
    // mynumber006-init-get-startscreencall
    MYNUMBER006_INIT_GET_STARTSCREENCALL_TEST: '/mynumber006-init-get-startscreencall',
    // mynumber008-init-get-mcas
    MYNUMBER008_INIT_GET_MCAS_TEST: '/mynumber008-init-get-mcas',
    // mynumber008-main-update-mcas
    MYNUMBER008_MAIN_UPDATE_MCAS_TEST: '/mynumber008-main-update-mcas',
    // common-cognito-id-merge
    COMMON_COGNITO_ID_MERGE: '/common-cognito-id-merge',
};

export default API;
