{"error_messages": {"not_valid_japanese": "{0}が正しくありません", "incorrect_email": "メールアドレスが正しくありません", "incorrect_phonenumber": "携帯電話番号が正しくありません", "main_required_jp": "入力必須項目です。", "required_field": "必須項目です。", "half_width_number": "半角数値のみで入力してください。", "equal_length_japanese": "{0}半角英数字{1}文字", "min_length_japanese": "{0}半角英数字{1}文字以上", "max_length_japanese": "{0}半角英数字{1}文字以下。", "password_not_match": "パスワードが一致していません", "send_pin_fail": "PINコードの送信に失敗しました。", "enter_half_with_number": "{0}桁の半角数値を入力してください。", "incorrect": "{0}が誤っています。", "exceeded_maximum_characters": "最大文字数を超過しています。", "exceed_file_size": "ファイルサイズの上限を超えています。", "system_error": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "min_length_number": "半角数字{0}文字で入力してください。", "exceed_file_size_10": "ファイルの登録に失敗しました。ファイルサイズを10MB以下にしてください。", "network_error": "現在接続できません。\nネットワークを確認して、後でもう一度お試しください。"}, "api": {"common": {"unknown_error": "エラーが発生しました。", "something_went_error": "異常が起こりました", "timeout": "コネクトタイムアウトが発生しました。"}}, "ERR_UNKNOWN": "エラーが発生しました。 (ERR_UNKNOWN)", "ERR_NETWORK": "ネットワークエラーが発生しました。しばらく時間をおいてから再度お試しください。", "ECONNABORTED": "エラーが発生しました。 (ECONNABORTED)", "ERR_BAD_REQUEST": "エラーが発生しました。 (ERR_BAD_REQUEST)", "ERR_BAD_RESPONSE": "エラーが発生しました。 (ERR_BAD_RESPONSE)", "ERR_UNAUTHORIZED": "認証エラーが発生しました。再度ログインしてください。", "contact001-main-send-inquiry-001-ERROR": "リクエストパラメータが異常です。", "contact001-main-send-inquiry-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "contact001-init-get-email-001-ERROR": "リクエストパラメータが異常です。", "contact001-init-get-email-002-ERROR": "システムエラーが発生しました。", "login001-main-get-token-002-ERROR": "リクエストパラメータが異常です。", "login001-main-get-token-003-ERROR": "データの取得に失敗しました。", "login001-main-get-token-004-ERROR": "ユーザーID又はパスワードが誤っています。", "login001-main-get-token-004-INFO": "ユーザーID又はパスワードが誤っています。", "login001-main-get-token-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "login001-main-get-token-005-WARNING": "このアカウントは現在ログインできません。", "login001-main-get-token-006-ERROR": "データの取得に失敗しました。", "login001-main-get-token-007-ERROR": "データの取得に失敗しました。", "login001-main-get-token-008-INFO": "ユーザーID又はパスワードが誤っています。", "login001-main-get-token-009-WARNING": "ユーザーID又はパスワードが誤っています。残り1回パスワードを間違えるとアカウントがロックされます。", "login001-main-get-token-010-ERROR": "データの取得に失敗しました。", "login001-main-get-token-011-WARNING": "ユーザーID又はパスワードが誤っています。アカウントがロックされました。パスワードをお忘れの場合からパスワードを再設定してください。", "login001-main-get-token-012-ERROR": "データの更新に失敗しました。", "login001-main-get-token-013-INFO": "ユーザーID又はパスワードが誤っています。", "login001-main-get-token-015-ERROR": "データの取得に失敗しました。", "login001-main-get-token-016-WARNING": "アカウントがロックされています。パスワードをお忘れの場合からパスワードを再設定してください。", "login001-main-get-token-017-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "login001-main-get-token-019-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "login001-main-get-enableflag-001-INFO": "リクエストパラメータ]", "login001-main-get-enableflag-002-ERROR": "リクエストパラメータが異常です。", "login001-main-get-enableflag-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "login000-main-get-entryenableflag-001-INFO": "リクエストパラメータ", "login000-main-get-entryenableflag-002-ERROR": "リクエストパラメータが異常です。", "login000-main-get-entryenableflag-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "news001-init-get-personal-news-list-002-ERROR": "システムエラーが発生しました。", "news001-init-get-personal-news-list-003-ERROR": "システムエラーが発生しました。", "news001-init-get-personal-news-list-004-ERROR": "システムエラーが発生しました。", "news001-init-get-personal-news-list-005-ERROR": "システムエラーが発生しました。", "news001-init-get-important-news-list-002-ERROR": "システムエラーが発生しました。", "news001-init-get-important-news-list-003-ERROR": "システムエラーが発生しました。", "news001-init-get-important-news-list-004-ERROR": "システムエラーが発生しました。", "news001-init-get-important-news-list-005-ERROR": "システムエラーが発生しました。", "news002-init-get-news-detail-002-ERROR": "システムエラーが発生しました。", "news002-init-get-news-detail-003-ERROR": "システムエラーが発生しました。", "news002-init-get-news-detail-004-ERROR": "システムエラーが発生しました。", "news002-init-get-news-detail-005-WARNING": "お知らせ情報の取得に失敗しました。", "news002-init-get-news-detail-006-ERROR": "システムエラーが発生しました。", "news002-init-get-news-detail-007-WARNING": "お知らせ情報の取得に失敗しました。", "coupon001-init-get-coupon-list-002-ERROR": "システムエラーが発生しました。", "coupon001-init-get-coupon-list-003-ERROR": "システムエラーが発生しました。", "coupon001-init-get-coupon-list-004-ERROR": "システムエラーが発生しました。", "coupon001-init-get-coupon-list-005-ERROR": "システムエラーが発生しました。", "coupon001-init-get-coupon-list-006-ERROR": "システムエラーが発生しました。", "coupon004-main-use-coupon-002-ERROR": "システムエラーが発生しました。", "coupon004-main-use-coupon-003-ERROR": "システムエラーが発生しました。", "coupon004-main-use-coupon-004-WARNING": "このクーポンは既に利用済みです。", "coupon004-main-use-coupon-005-ERROR": "システムエラーが発生しました。", "coupon003-init-get-coupon-detail-002-ERROR": "システムエラーが発生しました。", "coupon003-init-get-coupon-detail-003-ERROR": "システムエラーが発生しました。", "coupon003-init-get-coupon-detail-004-ERROR": "システムエラーが発生しました。", "coupon003-init-get-coupon-detail-005-ERROR": "システムエラーが発生しました。", "coupon003-init-get-coupon-detail-006-ERROR": "システムエラーが発生しました。", "coupon003-init-get-coupon-detail-007-ERROR": "システムエラーが発生しました。", "coupon003-init-get-coupon-detail-008-ERROR": "システムエラーが発生しました。", "coupon003-init-get-coupon-detail-009-ERROR": "システムエラーが発生しました。", "login001-init-get-banner-list-001-INFO": "", "login001-init-get-banner-list-002-ERROR": "システムエラーが発生しました。", "login001-init-get-keyvisual-list-001-ERROR": "リクエストパラメータが異常です。", "login001-init-get-keyvisual-list-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "home001-init-get-banner-list-002-ERROR": "システムエラーが発生しました。", "home001-init-get-news-unread-latest-001-ERROR": "リクエストパラメータが異常です。", "home001-init-get-news-unread-latest-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "home001-init-get-news-unread-latest-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "home001-init-get-news-unread-latest-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "home001-init-get-news-unread-latest-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "home001-init-get-coin-001-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "home001-init-get-coin-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "home001-init-get-coin-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "home001-init-get-coin-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "home001-init-get-coin-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "home001-init-get-coin-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "home001-init-get-coin-007-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "home001-init-get-coupon-list-002-ERROR": "システムエラーが発生しました。", "home001-init-get-coupon-list-003-ERROR": "システムエラーが発生しました。", "home001-init-get-coupon-list-004-ERROR": "システムエラーが発生しました。", "home001-init-get-coupon-list-005-ERROR": "システムエラーが発生しました。", "home001-init-get-coupon-list-006-ERROR": "システムエラーが発生しました。", "home001-init-get-news-unread-flag-002-ERROR": "システムエラーが発生しました。", "home001-init-get-news-unread-flag-003-ERROR": "システムエラーが発生しました。", "home001-init-get-news-unread-flag-004-ERROR": "システムエラーが発生しました。", "home001-init-get-news-unread-flag-005-ERROR": "システムエラーが発生しました。", "home001-init-get-userinfo-001-ERROR": "リクエストパラメータが異常です。", "home001-init-get-userinfo-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass002-main-update-password-001-ERROR": "リクエストパラメータが異常です。", "settingpass002-main-update-password-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass002-main-update-password-003-ERROR": "パスワードが正しくありません。", "settingpass002-main-update-password-004-ERROR": "パスワードは前回と異なるものを設定してください。", "settingpass002-main-update-password-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-send-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingpass001-main-send-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-send-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-send-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-send-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-send-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-send-authcode-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-send-authcode-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-check-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingpass001-main-check-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-check-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-check-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-check-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-check-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpass001-main-check-authcode-007-ERROR": "このコードは無効です。新しいコードをリクエストしてください。", "settingpass001-main-check-authcode-008-ERROR": "認証コードが誤っています。再度確認してください。", "deleteaccount001-main-check-authcode-001-ERROR": "リクエストパラメータが異常です。", "deleteaccount001-main-check-authcode-007-ERROR": "このコードは無効です。新しいコードをリクエストしてください。", "deleteaccount001-main-check-authcode-008-ERROR": " 認証コードが誤っています。再度確認してください。", "entry002-init-get-brandinfo-001-ERROR": "リクエストパラメータが異常です。", "entry002-init-get-brandinfo-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry002-init-get-brandinfo-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry002-main-send-authcode-001-ERROR": "リクエストパラメータが異常です。", "entry002-main-send-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry002-main-send-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry002-main-send-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry002-main-send-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry002-main-send-authcode-006-ERROR": "既にアカウント登録されています。", "entry002-main-send-authcode-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry002-main-send-authcode-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry002-main-send-authcode-009-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry003-main-check-authcode-002-ERROR": "リクエストパラメータが異常です。", "entry003-main-check-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry003-main-check-authcode-004-ERROR": "このコードは無効です。新しいコードをリクエストしてください。", "entry003-main-check-authcode-005-ERROR": "コードが間違っています。再度お試しください。", "entry006-main-get-token-002-ERROR": "リクエストパラメータが異常です。", "entry006-main-get-token-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry006-main-get-token-004-ERROR": "認証に失敗しました。しばらく時間をおいてから再度お試しください。", "entry007-main-get-token-002-ERROR": "リクエストパラメータが異常です。", "entry007-main-get-token-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry007-main-get-token-004-ERROR": "認証に失敗しました。しばらく時間をおいてから再度お試しください。", "entry007-main-get-token-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry006-main-register-account-001-ERROR": "リクエストパラメータが異常です。", "entry006-main-register-account-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry006-main-register-account-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry006-main-register-account-004-ERROR": "アカウントの登録に失敗しました。しばらく時間をおいてから再度お試しください。", "entry006-main-register-account-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry006-main-register-account-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry006-main-register-account-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry006-main-register-account-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry007-main-register-account-001-ERROR": "リクエストパラメータが異常です。", "entry007-main-register-account-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry007-main-register-account-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry007-main-register-account-004-ERROR": "アカウントの登録に失敗しました。しばらく時間をおいてから再度お試しください。", "entry007-main-register-account-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry007-main-register-account-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry007-main-register-account-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry007-main-register-account-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry007-main-register-account-009-ERROR": "既に登録されています。", "entry007-main-register-account-010-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry007-main-register-account-011-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "entry007-main-register-account-012-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset001-main-send-authcode-001-ERROR": "リクエストパラメータが異常です。", "reset001-main-send-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset001-main-send-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset001-main-send-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset001-main-send-authcode-005-ERROR": "ユーザーIDが存在しません。", "reset001-main-send-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset001-main-send-authcode-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset001-main-send-authcode-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset001-main-send-authcode-009-ERROR": "メール送信に失敗しました。", "reset001-main-send-authcode-009-INFO": "現在扱えないアカウントです。", "reset001-main-send-authcode-010-ERROR": "そのメールアドレスは使用できません。", "reset002-main-check-authcode-001-INFO": "リクエストパラメータ：[${0}]", "reset002-main-check-authcode-002-ERROR": "リクエストパラメータが異常です。", "reset002-main-check-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset002-main-check-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset002-main-check-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset002-main-check-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset002-main-check-authcode-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset002-main-check-authcode-008-ERROR": "このコードは無効です。新しいコードをリクエストしてください。", "reset002-main-check-authcode-009-ERROR": "コードが間違っています。再度お試しください。", "reset002-main-send-authcode-001-ERROR": "リクエストパラメータが異常です。", "reset002-main-send-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset002-main-send-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset002-main-send-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset002-main-send-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset002-main-send-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset002-main-send-authcode-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset002-main-send-authcode-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset002-main-send-authcode-009-ERROR": "メール送信に失敗しました。", "reset002-main-send-authcode-010-ERROR": "そのメールアドレスは使用できません。", "reset005-main-reset-password-001-ERROR": "リクエストパラメータが異常です。", "reset005-main-reset-password-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset005-main-reset-password-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset005-main-reset-password-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "reset005-main-reset-password-005-ERROR": "データの取得に失敗しました。", "reset005-main-reset-password-006-ERROR": "データの取得に失敗しました。", "settingmailmagazine001-main-update-mailmagazine-001-ERROR": "リクエストパラメータが異常です。", "settingmailmagazine001-main-update-mailmagazine-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel001-main-check-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingtel001-main-check-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel001-main-check-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel001-main-check-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel001-main-check-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel001-main-check-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel001-main-check-authcode-007-ERROR": "このコードは無効です。新しいコードをリクエストしてください。", "settingtel001-main-check-authcode-008-ERROR": "認証コードが誤っています。再度確認してください。", "settingtel001-main-send-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingtel001-main-send-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel001-main-send-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel001-main-send-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel001-main-send-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel001-main-send-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel001-main-send-authcode-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel001-main-send-authcode-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel002-main-send-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingtel002-main-send-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel002-main-send-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel002-main-send-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel002-main-send-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel002-main-send-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel003-main-check-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingtel003-main-check-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel003-main-check-authcode-003-ERROR": "このコードは無効です。新しいコードをリクエストしてください。", "settingtel003-main-check-authcode-004-ERROR": "認証コードが誤っています。再度確認してください。", "settingtel003-main-send-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingtel003-main-send-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel003-main-send-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel003-main-send-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel003-main-send-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel003-main-send-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtel003-main-update-tel-001-ERROR": "リクエストパラメータが異常です。", "settingtel003-main-update-tel-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-send-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingmail001-main-send-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-send-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-send-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-send-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-send-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-send-authcode-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-send-authcode-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-check-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingmail001-main-check-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-check-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-check-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-check-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-check-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail001-main-check-authcode-007-ERROR": "このコードは無効です。新しいコードをリクエストしてください。", "settingmail001-main-check-authcode-008-ERROR": "認証コードが誤っています。再度確認してください。", "settingmail002-main-send-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingmail002-main-send-authcode-002-ERROR": "入力されたメールアドレスは使用できません。", "settingmail002-main-send-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail002-main-send-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail002-main-send-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail002-main-send-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail003-main-send-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingmail003-main-send-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail003-main-send-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail003-main-send-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail003-main-check-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingmail003-main-check-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail003-main-check-authcode-003-ERROR": "このコードは無効です。新しいコードをリクエストしてください。", "settingmail003-main-check-authcode-004-ERROR": "認証コードが誤っています。再度確認してください。", "settingmail003-main-update-email-001-ERROR": "リクエストパラメータが異常です。", "settingmail003-main-update-email-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail003-main-update-email-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail003-main-update-email-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail003-main-update-email-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail003-main-update-email-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail003-main-update-email-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingmail003-main-update-email-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtop001-main-send-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingtop001-main-send-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtop001-main-send-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtop001-main-send-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtop001-main-send-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtop001-main-send-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtop001-main-send-authcode-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtop001-main-send-authcode-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingtop001-main-update-nickname-001-ERROR": "リクエストパラメータが異常です。", "settingtop001-main-update-nickname-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "chargecredit001-init-get-chargelimit-001-ERROR": "リクエストパラメータが異常です。", "chargecredit001-init-get-chargelimit-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargecredit001-init-get-chargelimit-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargecredit001-init-get-chargelimit-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargecredit002-main-execute-charge-001-ERROR": "リクエストパラメータが異常です。", "chargecredit002-main-execute-charge-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargecredit002-main-execute-charge-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargecnveni001-init-get-chargelimit-001-ERROR": "リクエストパラメータが異常です。", "chargecnveni001-init-get-chargelimit-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargecnveni001-init-get-chargelimit-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargecnveni001-init-get-chargelimit-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargecnveni002-main-execute-charge-001-ERROR": "リクエストパラメータが異常です。", "chargecnveni002-main-execute-charge-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargecnveni002-main-execute-charge-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnk001-init-get-chargelimit-001-ERROR": "リクエストパラメータが異常です。", "chargeintbnk001-init-get-chargelimit-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnk001-init-get-chargelimit-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnk001-init-get-chargelimit-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnk002-main-execute-charge-001-ERROR": "リクエストパラメータが異常です。", "chargeintbnk002-main-execute-charge-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpaytest001-main-register-bankpay-002-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-register-bankpay-003-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-register-bankpay-004-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-init-get-bankinfo-001-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-init-get-bankinfo-002-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-init-get-bankinfo-003-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-init-get-bankinfo-004-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-register-bankaccount-001-INFO": "リクエストパラメータ：[${0}]", "chargeintbnkpaytest001-main-register-bankaccount-002-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-register-bankaccount-003-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-register-bankaccount-004-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-register-bankaccount-005-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-register-bankaccount-006-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-execute-charge-001-INFO": "リクエストパラメータ：[${0}]", "chargeintbnkpaytest001-main-execute-charge-002-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-execute-charge-003-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-execute-charge-004-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-execute-charge-005-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-execute-charge-006-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-execute-charge-007-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-execute-charge-008-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-delete-bankaccount-001-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-delete-bankaccount-002-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-delete-bankaccount-003-ERROR": "システムエラーが発生しました。", "chargeintbnkpaytest001-main-delete-bankaccount-004-ERROR": "システムエラーが発生しました。", "chargeprepid001-main-execute-charge-001-ERROR": "リクエストパラメータが異常です。", "chargeprepid001-main-execute-charge-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeprepid001-main-execute-charge-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeprepid001-main-execute-charge-004-ERROR": "不正なシリアルコードです。", "chargeprepid001-main-execute-charge-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeprepid001-main-execute-charge-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeprepid001-main-execute-charge-007-ERROR": "不正なシリアルコードです。", "chargeprepid001-main-execute-charge-008-WARNING": "利用済みのシリアルコードです。", "chargeprepid001-main-execute-ocr-001-ERROR": "リクエストパラメータが異常です。", "chargeprepid001-main-execute-ocr-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "store003-init-get-store-detail-002-ERROR": "システムエラーが発生しました。", "store003-init-get-store-detail-003-ERROR": "システムエラーが発生しました。", "store003-init-get-store-detail-004-ERROR": "システムエラーが発生しました。", "store003-init-get-store-detail-005-ERROR": "システムエラーが発生しました。", "store003-init-get-store-detail-006-ERROR": "システムエラーが発生しました。", "store003-init-get-store-detail-007-ERROR": "システムエラーが発生しました。", "store001-main-get-store-query-002-ERROR": "システムエラーが発生しました。", "store001-main-get-store-query-003-ERROR": "システムエラーが発生しました。", "store001-main-get-store-query-004-ERROR": "システムエラーが発生しました。", "store001-main-get-store-query-005-ERROR": "システムエラーが発生しました。", "store001-main-get-store-query-006-ERROR": "システムエラーが発生しました。", "store001-main-get-store-query-007-ERROR": "システムエラーが発生しました。", "store001-main-get-store-query-008-ERROR": "システムエラーが発生しました。", "store001-main-get-store-query-009-ERROR": "システムエラーが発生しました。", "store001-init-get-store-query-001-ERROR": "システムエラーが発生しました。", "store001-init-get-store-query-002-ERROR": "システムエラーが発生しました。", "store001-init-get-store-query-003-ERROR": "システムエラーが発生しました。", "rocketssurvey001-init-get-survey-list-001-ERROR": "リクエストパラメータが異常です。", "rocketssurvey001-init-get-survey-list-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "rocketssurvey001-init-get-survey-list-003-ERROR": "アンケートの受付期間外です。", "rocketssurvey001-init-get-survey-list-004-ERROR": "アンケートの受付期間外です。", "rocketssurvey001-init-get-survey-list-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "rocketssurvey001-init-get-survey-list-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "rocketssurvey001-init-get-survey-list-007-ERROR": "データが見つかりませんでした。しばらく時間をおいてから再度お試しください。", "rocketssurvey001-init-get-survey-list-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "rocketssurvey001-init-get-survey-list-009-ERROR": "既に回答を受付済です。", "rocketssurvey002-main-send-answer-001-ERROR": "リクエストパラメータが異常です。", "rocketssurvey002-main-send-answer-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "rocketssurvey002-main-send-answer-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal003-main-update-address-001-ERROR": "システムエラーが発生しました。", "settingpersonal003-main-update-address-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal001-main-check-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingpersonal001-main-check-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal001-main-check-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal001-main-check-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal001-main-check-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal001-main-check-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal001-main-check-authcode-007-ERROR": "このコードは無効です。新しいコードをリクエストしてください。", "settingpersonal001-main-check-authcode-008-ERROR": "認証コードが誤っています。再度確認してください。", "settingpersonal001-main-send-authcode-001-ERROR": "リクエストパラメータが異常です。", "settingpersonal001-main-send-authcode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal001-main-send-authcode-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal001-main-send-authcode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal001-main-send-authcode-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal001-main-send-authcode-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal001-main-send-authcode-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal001-main-send-authcode-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "settingpersonal002-init-get-personalinfo-001-ERROR": "リクエストパラメータが異常です。", "settingpersonal002-init-get-personalinfo-002-ERROR": "リクエストパラメータが異常です。システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "common": {"button": {"cancel": "キャンセル", "next": "次へ", "change": "変更", "close": "とじる", "close_kanji": "閉じる", "send_pin": " PINコード送信", "forgot_pass": "パスワードをお忘れの場合", "forgot_zodiac": "干支と星座をお忘れの場合", "complete": "完了", "keep": "保存", "confirm": "確定", "open_google_map": "地図アプリで見る", "pay": "支払う", "pin_code_send": "認証コード送信", "back_to_setting": "設定に戻る", "back_button": "戻るボタン", "detail_button": "アイテム詳細", "icon_link": "{0} アイコン", "banner_link": "バナークリック", "ok_btn": "OK", "delete": "削除"}, "text": {"email": "メールアドレス", "password": "パスワード", "user_id": "ユーザーID", "zodiac": "干支・星座", "payment": "支払い", "yen_currency": "円", "news": "お知らせ一覧", "receivce_mail": "メールマガジン受信", "month": "月", "day": "日", "point": "pt", "point_jp": "点", "remain": "残り", "up_to_characters": "{0}文字まで", "language_choice": "言語選択", "top": "トップ", "include_tax": "(税込)", "click_here_for_user": "利用者はこちら", "login": "ログイン", "my_page": "マイページ", "member_number": "会員ナンバー", "back_button": "戻る", "yes": "はい", "no": "いいえ"}, "language": {"pop_js": "ja"}, "zip_code_not_found": "住所を検索できませんでした。"}, "setting": {"change_email": {"header": "メールアドレス変更", "pin": {"info": "に送信された認証コードを入力してください。", "tel_resend": "コードを再送信", "pin_code": "認証コード", "resend_to": "に認証コードを再送信しました。", "send_code": "認証コードを送信"}, "input": {"label_email": "新しいメールアドレス", "sub_label_email": "例：<EMAIL>", "placeholder_email": "メールアドレスを入力してください", "dialog_title": "ログイン用メールアドレスを", "dialog_message": "変更しました", "header": "新しいメールアドレスを入力してください", "max_length": "既定の桁数を超過しています。"}, "step": [{"text": "ワンタイムパスワード認証"}, {"text": "メールアドレス入力"}, {"text": "メールアドレス認証"}, {"text": "変更完了"}], "verify": {"notice_code": "に送信された認証コードを入力してください。", "verify_success_mess": "メールアドレスを変更しました", "re_send_title": "に送信された認証コードを入力してください。", "submit": "変更"}, "complete": {"screen_title": "変更完了", "title": "メールアドレスが\n変更されました", "button": "設定に戻る"}}, "change_tel": {"header": "電話番号変更", "header_complete": "変更完了", "input": {"title_form": "新しい電話番号を入力してください", "label_tel": "携帯電話番号（ハイフンなし）", "sub_label_tel": "例：***********", "placeholder_tel": "携帯電話番号を入力してください", "phone_length": "電話番号を入力してください", "first_three_char": "先頭の3文字は070、080、090で入力してください"}, "pass": {"label_pass": "パスワード", "placeholder_pass": "パスワードを入力してください", "info": "に送信された認証コードを入力してください", "resend_to": "に認証コードを再送信しました。", "pin_code": "認証コード"}, "zodiac": {"title_left": "ご自身の干支", "title_left2": "ご自身の星座", "title_right": "を選択してください。"}, "pin": {"info": "に送信された認証コードを入力してください。", "tel_resend": "コードを再送信", "pin_code": "PINコード", "dialog_title": "電話番号を変更しました"}, "step": [{"text": "ワンタイムパスワード認証"}, {"text": "電話番号入力"}, {"text": "SMS認証"}, {"text": "変更完了"}], "complete": "電話番号が\n変更されました"}, "change_zodiac": {"header": "干支・星座を変更", "current": {"title_left": "現在設定している干支", "title_left1": "現在設定している星座", "title_right": "を選択してください。"}, "new": {"title_top_left": "登録する干支", "title_top_left1": "登録する星座", "title_top_right": "を選択してください。", "title_bottom_left": "ログイン時の", "title_bottom_mid": "本人確認で使用", "title_bottom_right": "します。", "dialog_title": "干支・星座を変更しました"}, "step": [{"text": "干支の認証"}, {"text": "星座の認証"}, {"text": "干支の登録"}, {"text": "星座の登録"}, {"text": "変更完了"}]}, "change_pass": {"dialog_title": "パスワードを変更しました", "header": "パスワード変更", "pin": {"info": "に送信された認証コードを入力してください。", "pin_code": "認証コード", "tel_resend": "コードを再送信", "resend_to": "に認証コードを再送信しました。", "next_button": "認証コードを送信"}, "step": [{"text": "ワンタイムパスワード認証"}, {"text": "パスワード入力"}, {"text": "変更完了"}], "complete": {"screen_title": "変更完了", "title": "パスワードが\n変更されました", "button": "設定に戻る"}, "label_old_pass": "現在のパスワード", "sub_label_old_pass": "半角英数字8文字以上", "placeholder_old_pass": "現在のパスワードを入力してください", "label_pass": "新しいパスワード", "sub_label_pass": "半角英数字8文字以上", "placeholder_pass": "新しいパスワードを入力してください", "label_confirm_pass": "新しいパスワード再入力", "placeholder_confirm_pass": "新しいパスワードを再入力してください", "special_number_char": "パスワードは記号で入力してください。", "password_not_match": "パスワードとパスワード（確認）が異なっています。", "wrong_password": "パスワードが違います。"}, "withdraw": {"info": "に送信された認証コードを入力してください。", "tel_resend": "コードを再送信", "pin_code": "認証コード", "placeholder_reason": "退会理由を入力してください", "label_reason": "退会理由", "header": "退会する", "dialog_title": "退会を受け付けました", "dialog_message1": "ご利用ありがとうございました。", "dialog_message2": "またのご利用をお待ちしております。", "button_withdraw": "停止", "label_checkbox": "確認しました", "dialog_confirm_title": "本当に退会しますか？", "dialog_confirm_message1": "本サービスのすべてのデータが削除されます", "dialog_confirm_message2": "データは元に戻すことができません", "resend_pin": "{0}に認証コードを再送信しました。"}, "delete_account": {"003": {"header": "退会を受け付けました", "title1": "ご利用ありがとうございました", "title2": "またのご利用をお待ちしております"}}, "choose_image": {"title_collection": "写真ライブラリ", "title_camera": "写真またはビデオを撮る", "title_folder": "フアイルを選択"}, "menu_setting": {"header": "設定", "user_info": "ユーザー情報", "alert_change_name": "ニックネームを変更しました", "alert_copy": "ユーザーIDをコピーしました", "button_verify": "本人確認する", "button_copy": "コピー", "label_email": "メールアドレス（ユーザーID）", "label_tel": "電話番号", "label_noti": "通知を受け取る", "label_google": "Googleカレンダーを同期する", "button_withdraw": "退会する", "label_avatar": "アイコン変更", "button_update": "更新", "account_information": "個人情報（住所・氏名など）", "receive_mail": "メールマガジン受信", "receive_mail_success": "メールマガジン受信を変更しました", "accept_receive_mail": "受信する", "not_receive_mail": "停止する", "external_service": "外部サービス連携", "icon_change": "アイコン変更", "confirm_cancel_line_mesage": "退会前にLINEとの連携を解除します", "confirm_link_line_mesage": "LINEと連携を行います", "confirm_unlink_line_mesage": "LINEとの連携を解除しますか？"}, "change_receive_mail": {"stop_title": "メールマガジンの配信を\n停止しますか？", "stop_info": "配信を停止すると、\nオトクな情報が受け取れなくなってしまいます", "button_stop": "停止", "stop_success_title": "メールマガジンの配信を\n停止しました", "stop_success_info": "再配信を希望する場合は\n設定から更新してください", "receive_title": "メールマガジンの配信を\n受信しますか？", "receive_info": "配信を受信すると、\nオトクな情報が受け取れます", "button_receive": "受信", "receive_success_title": "メールマガジンの配信を\n受信しました", "receive_success_info": "オトクにお買い物を楽しみましょう！"}, "change_personal_info": {"verify_code": "個人情報変更", "personal_info_view": {"header": "個人情報変更", "full_name": "氏名", "not_change": "変更できません", "my_number_already_confirmed": "マイナンバー確認済みのため変更できません", "dob": "生年月日", "gender": "性別", "address": "住所", "gender_male": "男性", "gender_female": "女性", "gender_other": "その他", "back_to_setting": "設定へ戻る"}, "error_message": {"invalid_code": "このコードは無効です。新しいコードをリクエストして下さい。", "incorrect_code": "認証コードが誤っています。再度確認してください。"}, "change_address": {"header": "変更内容", "postal_code": "郵便番号", "postal_code_sub_label": "例：1234567", "postal_code_placeholder": "郵便番号を入力してください", "cannot_find_address": "住所を検索できませんでした。\n以下のフォームをご入力ください。", "prefectures": "都道府県", "prefectures_placeholder": "選択してください", "municipality": "市区町村", "municipality_sub_label": "例：江東区新木場", "municipality_placeholder": "市区町村を入力してください", "neigh_bour_hood": "町域", "neigh_bour_hood_sub_label": "例：新木場", "neigh_bour_hood_placeholder": "町域を入力してください", "address": "町域・丁目番地号", "address_sub_label": "例：1-18-7", "address_placeholder": "町域・丁目番地号を入力してください", "building_name": "建物名", "building_name_sub_label": "例：NESビルディング 101", "building_name_placeholder": "建物名を入力してください", "complete_message": "住所を変更しました"}, "pin": {"info": "に送信された認証コードを入力してください", "pin_code": "認証コード", "tel_resend": "コードを再送信", "resend_to": "に認証コードを再送信しました。", "send_code": "認証コードを送信"}, "complete": {"header": "変更完了", "title": "個人情報が\n変更されました"}}}, "register": {"register_step": [{"text": "携帯番号入力"}, {"text": "ワンタイムパスワード認証"}, {"text": "ユーザー情報登録"}, {"text": "利用規約の確認"}, {"text": "登録完了"}], "register_step_sms": [{"text": "携帯番号入力"}, {"text": "ワンタイムパスワード認証"}, {"text": "ユーザー情報登録"}, {"text": "利用規約の確認"}, {"text": "登録完了"}], "register_step_email": [{"text": "メールアドレス入力"}, {"text": "ワンタイムパスワード認証"}, {"text": "ユーザー情報登録"}, {"text": "利用規約の確認"}, {"text": "登録完了"}], "terms": {"header": "新規登録", "terms_of_use": "利用規約", "agree_term_of_use": "利用規約に同意する", "agree_commercial_law": "個人情報の取り扱いについて了承する", "read_and_agree_before_start": "を最後までお読みいただき、同意の上、利用を開始してください。", "article_1_header": "第１条（本利用規約の目的）", "article_1_content": "ダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキスト", "article_2_header": "第2条（ダミーテキスト）", "article_2_1_content": "1. ダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキスト", "article_2_2_content": "2. ダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキスト", "article_3_header": "第3条（ダミーテキスト）", "article_3_1_content": "1. ダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキストダミーテキスト", "article_3_2_content": "2. ダミーテキストダミーテキストダミーテキストダミーテキストダミーテキスト", "article_3_2_subpoints": ["(ア) 本利用規約に違背して本サービスを利用することが明らかに予想される場合", "(イ) 当社に対して負担する債務の履行について現に遅滞が生じている場合又は過去において遅滞の生じたことがある場合", "(ウ) 本サービスの申込に際して当社に対し虚偽の事実を申告した場合", "(エ) 申込の際に未成年者、成年被後見人、被保佐人又は被補助人であって、自らの行為によって確定的に利用契約を締結する行為能力を欠き、法定代理人その他の同意権者の同意又は追認があったことが当社において確認できない場合。", "(オ) 反社会的勢力に該当する場合。", "(カ) 本人確認を行うことができない場合。", "(キ) 前各号に定める場合のほか、当社が業務を行ううえで支障がある場合又は支障の生じる恐れがある場合。"], "article_4_header": "第4条（本サービスの内容）", "article_4_content": "1. 本サービスは、クラウドサーバーおよびブロックチェーンシステム上で財産目録の作成、電子契約の締結、保管及び管理を行うことのできるオンライン型の相続支援サービスです。主に次の機能をお客さまへ提供します。", "article_4_subpoints": ["(ア) 写真投稿ないしテキスト入力等による財産目録作成機能", "(イ) 写真や手紙等のデジタルデータの保管機能", "(ウ) 財産目録の表示およびダウンロード機能", "(エ) 本人認証を行うことで利用できる電子実印機能", "(オ) 財産目録に記載した資産の相続手続を円滑にすることを目的とした民事信託契約書の作成機能", "(カ) 任意のお客さまを対象に民事信託契約書を締結・管理する電子契約機能", "(キ) 上記機能の不正利用を防止するための本人認証機能"], "button_back": "戻る", "button_agree_and_register": "同意して新規登録"}, "account_verify_code_send": {"verify_text": "携帯電話番号を入力してください。", "1": {"for_verify": "SMS", "enter": "携帯電話番号", "label": "携帯電話番号（ハイフンなし）", "sub_label": "例：***********", "placeholder": "携帯電話番号を入力してください"}, "2": {"for_verify": "メール", "enter": "メールアドレス", "label": "メールアドレス", "sub_label": "例：<EMAIL>", "placeholder": "メールアドレスを入力してください"}, "verification_text": "{0}による本人確認を行うため、{1}を入力してください。", "telephone_number": "携帯電話番号", "email": "メール", "email_address": "メールアドレス", "enter_valid_value": "正しく入力してください。", "exceed_number_of_digits": "既定の桁数を超過しています。", "button": {"send_verification_code": "認証コードを送信"}}, "account_info": {"verification_text": "{0}による本人確認を行うため、{1}を入力してください", "telephone_number_text": "携帯電話番号", "email": "メール", "telephone_number_label": "携帯電話番号", "email_address_label": "メールアドレス", "email_address": "メールアドレス", "telephone_number_sub_label": "例：***********", "telephone_number_placeholder": "携帯電話番号を入力してください", "email_sub_label": "例：<EMAIL>", "email_placeholder": "メールアドレスを入力してください", "email_invalid_message": "メールアドレスが正しくありません", "email_incorrect_message": "メールアドレスが誤っています。", "nickname_label": "ニックネーム", "nickname_sub_label": "アプリ内で利用するニックネームです", "nickname_placeholder": "ニックネームを入力してください", "password_text": "パスワード", "password_label": "パスワードの設定", "password_sub_label": "半角英数字8文字以上で設定ください", "password_placeholder": "パスワードを入力してください", "password_incorrect_message": "パスワードが誤っています。", "confirm_password_label": "パスワード再入力", "confirm_password_placeholder": "再度パスワードを入力してください", "send_verification_code": "認証コード送信", "button_pin_code_send": "PINコード送信", "account_creation_failed_message": "アカウント作成失敗", "up_to_characters": "{0}文字以内", "gender": "性別", "gender_option": ["男性", "女性", "その他", "回答しない"], "gender_male": "男性", "gender_women": "女性", "gender_others": "その他", "gender_private": "回答しない", "age": "年齢", "age_placeholder": "選択してください", "residence": "居住地", "residence_placeholder": "選択してください", "full_name": "氏名", "full_name_sub_label": "本名を入力してください", "full_name_placeholder": "本名を入力してください", "last_name_placeholder": "姓", "invalid_last_name": "無効な姓です。", "first_name_placeholder": "名", "invalid_first_name": "無効な名です。", "date_of_birth": "生年月日", "year_of_birth_placeholder": "西暦", "month_of_birth_placeholder": "月", "day_of_birth_placeholder": "日", "postal_code": "郵便番号", "postal_code_sub_label": "例：1234567", "postal_code_placeholder": "郵便番号を入力してください", "cannot_find_address": "存在しない郵便番号です", "address_not_exist": "存在しない住所です", "prefectures": "都道府県", "prefectures_placeholder": "選択してください", "municipalities": "市区町村", "municipalities_sub_label": "例：江東区新木場", "municipalities_placeholder": "市区町村を入力してください", "town_area": "町域", "town_area_sub_label": "例：新木場", "town_area_placeholder": "町域を入力してください", "address": "町域・丁目番地号", "address_sub_label": "例：1-18-7", "address_placeholder": "町域・丁目番地号を入力してください", "building_name": "建物名", "building_name_sub_label": "例：NESビルディング 101", "building_name_placeholder": "建物名を入力してください", "receive_mail_magazine": "メールマガジンを受け取る", "choose_year_month_first": "年と月を選択する必要があります", "password_must_have_letter_digit": "パスワードには英字、数字を含めてください。", "password_must_have_specific_chars": "パスワードは記号で入力してください。", "password_not_match": "パスワードとパスワード（確認）が異なっています。", "button": {"address_autofill": "住所自動入力", "sign_up": "新規登録"}, "my_number_info": "マイナンバーカード連携", "my_number_detail": "マイナンバーカードから本人確認を行う場合、「本人確認をする」をクリックして下さい。", "my_number_button": "本人確認をする"}, "account_verify": {"enter_verification_code": "{0}に送信された認証コードを入力してください", "resend_pin_code_text": "コードを再送信", "resend_pin_code_message": "{0}に認証コードを再送信しました。", "verification_code": "認証コード", "button_next": "次へ", "error_message": {"invalid_code": "このコードは無効です。新しいコードをリクエストして下さい。", "incorrect_code": "認証コードが誤っています。再度確認してください。"}}, "user_information": {"user_name": "ニックネーム", "postcode": "郵便番号", "municipality": "市区町村", "address": "町域・番地", "title": "新規登録", "user_name_sub": "ふくウォレット内で利用するニックネームです", "user_name_placeholder": "ニックネームを入力してください", "gender": "性別", "gender_male": "男性", "gender_women": "女性", "gender_others": "その他", "gender_private": "回答しない", "birthday": "生年月日", "birthday_placeholder": "生年月日を入力してください", "postcode_description": "▼郵便番号を入力すると、住所の一部が自動的に表示されます。", "postcode_placeholder": "郵便番号を入力してください", "prefectures": "都道府県", "prefectures_placeholder": "選択してください", "municipality_placeholder": "市区町村を入力してください", "municipality_description": "▼町域以降を入力してください", "address_placeholder": "番地を入力してください", "municipality_description_2": "例)〇〇町⇒〇〇町〇丁目〇番〇号", "building": "建物名など", "building_placeholder": "建物名入力してください", "btn_submit": "新規登録"}, "zodiac_register": {"title": "新規登録", "please_select": " を選択してください。", "select_1": "ご自身の星座", "select_2": "ご自身の干支", "login_time": "ログイン時の", "identity_verification": "本人確認で使用", "to_do": "します。"}}, "application_form": {"title": {"picture_text": "出産・子育て応援ギフト 申し込みフォーム", "gift_text": "既に商品券を購入・利用されている方は下記のボタンよりお申し込みください。", "gift_info1": "既に商品券を", "gift_info2": "購入・利用している方", "header_text": "購入者申し込みフォーム", "comfirm_text": "入力情報に間違いがないか、ご確認ください", "regulation_text": "NESPay 利用規約", "checkbox": "利用規約に同意する", "warning_text": "ご確認ください", "applicated": "申し込みを受け付けました"}, "label": {"set": "申し込みセット数", "full_name": "名前", "fugirana": "フリガナ", "postcode": "郵便番号", "prefectures": "都道府県", "municipalities": "市区町村", "townArea": "町域", "address": "番地", "buildingName": "建物名", "contact": "携帯電話番号", "mailAddress": "メールアドレス"}, "sub_label": {"full_name": "例：山田 太郎", "fugirana": "例：ヤマダ タロウ", "postcode": "例：1234567", "municipalities": "例：江東区", "townArea": "例：新木場", "address": "例：1-18-7", "buildingName": "例：NESビルディング 101", "contact": "例：***********", "mailAddress": "例：<EMAIL>"}, "place_holder": {"full_name": "メールアドレスを入力してください", "fugirana": "フリガナを入力してください", "postcode": "郵便番号を入力してください", "prefectures": "選択してください", "municipalities": "市区町村を入力してください", "townArea": "町域を入力してください", "address": "番地を入力してください", "buildingName": "番地を入力してください", "contact": "携帯電話番号を入力してください", "mailAddress": "メールアドレスを入力してください"}, "step": [{"text": "セット数指定"}, {"text": "申込者情報入力"}, {"text": "申込者情報確認"}, {"text": "申し込み完了"}], "button": {"get_code": "住所自動入力", "navigate_confirm": "確認画面へ", "back": "戻る", "application": "申し込み", "close": "とじる"}, "warning_content": "申し込み完了メールは {0}から送信いたします。{0}のメールが受信できるように設定をお願いいたします。"}, "login": {"screen_title": "ログイン", "user_id_placeholder": "登録したメールアドレスを入力してください", "password_sub_label": "半角英数字8文字以上", "password_placeholder": "登録したパスワードを入力してください", "incorrect_user_id_or_pass": "ユーザーID又はパスワードが誤っています。", "title_sign_up": "アカウントをお持ちでない方は、新規登録をしてください。", "email_invalid_message": "メールアドレスが正しくありません", "out_side_service_id_login": "外部サービスIDでログイン", "button": {"sign_in": "ログイン", "sign_up": "新規登録", "forgot_password": "パスワードをお忘れの場合", "line_login_button": "LINEでログイン", "yahoo_login_button": "Yahoo! JAPAN IDでログイン"}, "text": {"outOfPeriodTitle": "ご利用期間外です", "periodDetail": "ご利用可能期間:", "close_button": "とじる", "timeTile": "まで"}}, "login000": {"login_Info": "アカウントをお持ちの方はこちら", "login_button": "ログイン", "sign_up_info": "アカウントをお持ちでない方はこちら", "sign_up_button": "新規登録", "text": {"outOfPeriodTitle": "ご利用期間外です", "periodDetail": "ご利用可能期間:", "close_button": "とじる", "timeTile": "まで"}}, "payment": {"payment_gift": {"used_gift_label": "使用する対象", "store_name_label": "店舗名", "payment_amount_label": "支払い金額", "payment_point_label": "支払うポイント", "coupon_discount_label": "クーポン割引", "store_input": {"title": "支払う店舗を指定します", "store_code_label": "店舗アドレス(半角数字10桁)", "store_code_sub_label": "店舗アドレスは各店舗でご確認ください", "store_code_placeholder": "店舗アドレスを入力してください"}, "input": {"title_1": "金額を入力して、支払い内容を", "title_2": "確定してください", "payment_amount_placeholder": "支払い金額を入力してください", "over_current_balance": "残高以内の金額を入力ください。", "balance": "残高"}, "confirm": {"header": "支払い内容確認", "title": "お支払い内容をご確認ください", "date": "支払日時", "checkbox_text": "この画面をお店の人に見せました", "button": {"submit": "この内容で支払う"}}, "complete": {"header": "支払い完了", "title": "支払いが完了しました", "payment_amount_after_discount": "割引後の支払い金額", "button": {"to_top": "{0}トップへ", "to_home": "ホームに戻る"}}}, "paysingle005": {"of": "を", "amount": "pt 取得しました"}, "paymulti006": {"of": "を", "amount": "pt 取得しました"}, "home_btn": "ホームに戻る", "may_already_completed": "既に決済が完了している可能性があります。一度ホーム画面に戻り、ご利用履歴をご確認ください。"}, "reset_pass": {"header": "パスワードリセット", "input": {"title": "メールによる本人確認を行うため、メールアドレスを入力してください。", "email_sub_label": "例：<EMAIL>", "email_placeholder": "登録したメールアドレスを入力してください"}, "pin": {"info": "{0}に送信された認証コードを入力してください。", "resend_to": "{0}に認証コードを再送信しました。", "resend_code": "コードを再送信", "pin_code": "PINコード", "new_pin_code": "認証コード", "exp_time": "このコードは無効です。新しいコードをリクエストして下さい。", "wrong_pin_code": "認証コードが誤っています。再度確認してください。"}, "zodiac": {"step_1": "ご自身の干支", "step_2": "ご自身の星座", "choose": "を選択してください。"}, "confirm": {"header": "パスワード再設定", "password_reset_label": "新しいパスワード", "password_reset_sub_label": "半角英数字8文字以上", "password_reset_placeholder": "新しいパスワードを入力してください", "password_confirm_label": "新しいパスワード再入力", "password_confirm_sub_label": "半角英数字8文字以上", "password_confirm_placeholder": "新しいパスワードを再入力してください", "button": {"submit": "変更"}, "dialog": {"title": "パスワードを変更しました", "sub_title": "変更したパスワードでログインできます"}, "password_notice": "パスワードには英字、数字を含めてください。", "password_not_match": "パスワードとパスワード（確認）が異なっています。"}, "complete": {"header": "変更完了", "title": "パスワードが\n変更されました", "back_to_login": "ログインに戻る"}, "button": {"send_code": "認証コードを送信"}, "text": {"email": "メールアドレスの形式が正しくありません。"}, "step": [{"text": "ワンタイムパスワード認証"}, {"text": "パスワード再設定"}, {"text": "設定完了"}]}, "store_detail": {"title": "店舗詳細", "business_hours_title": "営業時間", "location_title": "アクセス", "route_public_transport_heading": "電車・バスで", "route_car_heading": "お車で", "notes_heading": "注意事項", "price_heading": "円分使えます", "detail_pay_heading": "お持ちのカード・商品券で", "regular_holiday": "定休日", "dealing_medal_headline": "使える対象", "HP_link": "店舗公式HP", "payment_type_headline": "支払い方式", "app_qr_scanning": "アプリ（QR読取）", "card_qr_scanning": "カード（QR表示）"}, "store_list": {"screen_name": "取扱店一覧", "show_more_item": "次の{0}件を表示", "search_narrow_down": "検索・絞り込み", "search_with_gift": "出産・子育て応援ギフトが使える", "search_result": "検索結果", "unit": "件", "medal_service_tail": "が使える", "area_name_tail": "にある", "data_store_not_found": "検索条件に該当するお店は存在しません"}, "store_store": {"header": "絞り込み", "use_gift_certificate_point_headline": "使用する対象", "store_type_headline": "店舗の種類", "area_headline": "エリア（地区）", "search_button": "この内容で絞り込み", "keyword_headline": "キーワード", "keyword_input_placeholder": "住所や店名を入力してください", "placeholder_dropdown": "すべて", "key_word": "キーワード"}, "charge": {"charge_optional": {"title": "入金する方法を選択してください", "header": "チャージ方法の選択", "credit_card": {"title": "クレジットカードで入金", "text": "クレジットカード", "message": "指定した金額をクレジットカードからチャージします", "button": "クレジットカードでチャージ"}, "convenient_store": {"title": "コンビニ決済で入金", "text": "コンビニ決済", "message": "コンビニエンスストアで所定の手続きを行いチャージします", "button": "コンビニ決済でチャージ"}, "bankpay": {"message": "Bank Pay（バンクペイ）経由でチャージします", "button": "Bank Payでチャージ"}, "bank": {"title": "銀行決済で入金", "text": "銀行決済", "message": "Pay-easy（ペイジー）経由でチャージします", "button": "Pay-easyでチャージ"}, "serial_code": {"text": "プリペイドカード", "message": "購入したプリペイドカードからチャージします", "link": "プリペイドカードの販売についてはこちら", "button": "プリペイドカードでチャージ"}, "qr_code": {"text": "QRコード", "message": "QRコードを読み取って入金します。", "button": "QRコードで入金"}, "qr_code_reading": {"text": "QRコード", "message": "QRコードを読み込んでチャージします", "button": "QRコードを読込"}, "qr_code_display": {"text": "QRコード", "message": "QRコードを表示してチャージします", "button": "QRコードを表示"}}, "charge_input": {"card": {"title": "チャージ金額入力", "text": "クレジットカード", "message": "指定した金額をクレジットカードから入金します。", "button": "クレジットカードで入金"}, "cvs": {"title": "チャージ金額入力", "text": "コンビニ決済", "message": "コンビニエンスストアで所定の手続きを行い入金します。", "button": "コンビニ決済で入金"}, "bank": {"title": "チャージ金額入力", "text": "銀行決済", "message": "インターネットバンキングで入金します。", "button": "銀行決済で入金"}, "message1": "入金する金額を選択するか、", "message2": "フォームに金額を直接入力してください", "label": "金額を入力", "sub_label": "チャージは1,000円単位です", "place_holder": "金額を入力してください", "deposit_charge_limit": "あと{0}円入金できます", "deposit_charge_limit_1": "あと", "deposit_charge_limit_2": "円チャージできます", "button": {"cancel": "キャンセル", "confirm": "決定"}}, "charge_confirm": {"title": "チャージ内容の確認", "message": "入金内容をご確認ください", "total_text": "チャージ金額", "amount": "購入金額", "confirm_value": "入金総額", "message_footer": "「決定」ボタンを押下すると提携先の株式会社DGフィナンシャルテクノロジーの決済画面が表示されますので、引き続き決済手続きを進めてください。", "accept": "決定", "message_err": "エラーが発生しました。", "error_text_1": "商品が存在しないためチャージすることができません。", "error_text_4": "チャージ後の残高が", "error_text_4_1": "円を超えるためチャージすることができません。", "error_text_default": "充電できません。"}, "charge_complete": {"card": {"title": "チャージ完了", "made_deposit": "チャージしました", "charge_date_title": "有効期限"}, "cvs": {"title": "申込完了", "made_deposit": "申込みが完了しました", "charge_date_title": "支払期限"}, "bank": {"title": "申込完了", "made_deposit": "申込みが完了しました", "charge_date_title": "支払期限"}, "reason": "出産・子育て応援ギフト", "unit": "円", "button": "ホームに戻る", "expire_date": "有効期限：", "message_err": "チャージに失敗しました。", "message_err1": "決済処理でエラーが発生しました。"}, "charge_gift": {"quantity": {"button_buy": "購入する", "title": "商品券を購入", "number_of_purchases": "購入数を指定", "unit": "セット", "minutes": "分"}, "method": {"title": "チャージ方法の選択", "description_1": "チャージする方法を選択してください。", "description_2": "提携先の株式会社DGフィナンシャルテクノロジーの決済画面が表示されますので、引き続き決済手続きを進めてください。", "credit_type": "クレジットカード", "credit_type_description": "指定した金額をクレジットカードで購入します", "button_charge_credit": "クレジットカードで購入", "convenience_store_type": "コンビニ決済", "convenience_store_type_description": "コンビニエンスストアで所定の手続きを行い購入します", "button_convenience_store": " コンビニ決済で購入", "error": {"can_not_purchase": "商品が存在しないためチャージすることができません。", "can_not_charge": "充電できません。", "unknown": "エラーが発生しました。", "balance_after_charging": "チャージ後の残高が", "can_not_charge_because_exceeds": "円を超えるためチャージすることができません。"}}, "complete": {"title": "チャージ完了", "unit": "セット", "description": "購入しました", "reason": "出産・子育て応援ギフト"}}, "charge_qr_code": {"scan": {"title": "QRコードで入金"}, "input": {"title": "QRコードで入金", "lbl_input_deposit": "入金用コードを入力", "sub_lbl_input_deposit": "入金用コードはQRコードの下にある10ケタの英数字です", "placeholder_input_deposit": "入金用コードを入力してください", "btn_submit": "確定"}, "complete": {"title": "チャージ完了", "unit": "セット", "description": "チャージしました", "reason": "出産・子育て応援ギフト"}}, "charge_qr_code_read": {"001": {"title": "QRコードでチャージ"}, "002": {"title": "QRコードチャージ", "lbl_input_deposit": "チャージ用コードを入力", "sub_lbl_input_deposit": "チャージ用コードはQRコードの下にある10ケタの数字です。", "placeholder_input_deposit": "入力してください", "btn_submit": "決定", "enter_half_with_number": "{0}ケタの半角数字を入力してください。"}, "003": {"title": "チャージ完了", "unit": "セット", "description": "チャージしました", "reason": "出産・子育て応援ギフト", "deposited": "チャージしました", "acquired": "取得しました", "yen": "円", "point": "pt"}}, "qr_common": {"qr_description": "QRコードが読み取れない", "qr_header": "カメラをQRコードに向けてください", "permission_error": "カメラ起動エラー", "close_dialog_permission": "閉じる", "guide_permission": "カメラの起動が許可されませんでした。\nカメラの起動を拒否された場合は一度ブラウザの更新ボタンを押してから、カメラの起動を許可していただくか\n宛先を手入力またはアドレス帳よりご入力ください。\n確認画面が出ない場合、iOSの場合、設定＞safari>カメラのアクセスを許可の変更を行なってください。\n(当機能はiOSの場合:最新のSafari Androidの場合:最新のchromeのみ対応しております)"}, "charge_serial_code": {"scan": {"tap_serial_code": "シリアルコードをタップしてください", "read_failed": "読み取り失敗", "scan_failed": "シリアルコードの読み取りに失敗しました。", "permission_error": "カメラ起動エラー", "permission_error_message": "カメラの起動でエラーが発生しました。\nカメラが接続されているか、 またはカメラのアクセスが許可されているかをご確認ください。", "qr_title": "シリアルコード読み取り", "serial_code_scan_header": "カメラをシリアルコードに向けてください", "camera_not_ready": "Camera is not ready!", "read_serial_code_failure": "読取りに失敗しました", "btn_capture": "撮影", "btn_confirm": "確定"}, "input": {"title": "シリアルコード入力", "error": {"coupon_error": "クーポン情報の取得に失敗しました。画面の更新を実施してください。\n状況が改善しない場合は、時間をおいてから再度お試しください。", "fail_to_charge": "チャージに失敗しました。", "code_not_exist": "入力されたシリアルコードは存在しません。", "code_been_used": "入力されたシリアルコードは使用済みです。", "invalid": "無効なシリアルコードです。", "balance_error_1": "チャージ後の残高が", "balance_error_2": "円を超えるためチャージすることができません。"}, "description": "シリアルコードはプリペイドカードの裏面に記載されている16ケタの英数字です。", "btn_read": "読み取る", "enter_serial_code": "シリアルコードを入力", "input_serial_code_placeholder": "入力してください", "btn_submit": "決定"}, "complete": {"title": "チャージ完了", "unit": "円", "description": "チャージしました", "reason": "出産・子育て応援ギフト"}}, "charge_bankpay": {"title": "BankPay決済テスト", "button": {"register": "会員登録", "account_register": "銀行口座登録", "charge": "チャージ", "account_delete": "銀行口座削除"}, "messages": {"success_register": "会員登録が完了しました", "success_account_register": "口座登録が完了しました", "success_charge": "チャージが完了しました", "success_account_delete": "口座削除が完了しました"}}, "bnk_pay": {"bnk_pay001": {"title": "銀行口座の選択", "register_info": "チャージ可能な銀行口座が\n登録されていません", "register_btn": "銀行口座を登録する", "message_fail": "銀口座登録に失敗しました\n再度お試しください"}, "bnk_pay002": {"title": "銀行口座の選択", "register_info": "登録する銀行口座を選択してください", "mess_register_fail": "銀行口座登録に失敗しました\n再度お試しください", "char_bank_name": "行"}, "bnk_pay004": {"title": "銀行口座登録完了", "account_register_complete_info": "銀行口座を登録しました", "charge_page_btn": "チャージへ進む"}, "bnk_pay005": {"title": "チャージ金額入力", "message1": "入金する金額を選択するか、", "message2": "フォームに金額を直接入力してください", "label_input": "金額入力", "sub_label_input": "入金は1,000円単位になります。", "max_label_input": "あと{0}円入金できます。", "register_bank_account_btn": "口座登録画面に進む", "message_not_complete": "口座が登録されていません。", "account_disable": "他のユーザーが同一の", "account_disable_1": "口座を登録した恐れがあります。", "account_disable_2": "口座登録をやり直してください。", "account_disable_3": "エラーが発生しました\n恐れ入りますが\n口座登録をやり直してください", "deposit_type_one": "普通口座", "deposit_type_two": "口座口座"}, "bnk_pay006": {"title": "チャージ内容の確認", "message_ten_minus": "1分後に再度お試しください", "message_footer": "「決定」ボタンを押下すると提携先の株式会社DGフィナンシャルテクノロジーの決算画面が表示されますので、引き続き決算手続きを進めてください。", "title_bank_name": "チャージ方法", "char_end_bank_name": "決済"}, "bnk_pay007": {"title": "チャージ完了", "charge_complete_info": "チャージしました", "home_btn": "ホームに戻る", "charge_date_title": "有効期限"}, "bnk_pay008": {"title": "銀行口座削除", "del_complete_info": "銀行口座を削除しました", "account_del_btn": "銀行口座を削除", "account_del_comfirm_info": "現在登録している銀行口座を\n削除してもよろしいですか？", "no_btn": "いいえ", "yes_btn": "はい"}, "bnk_pay009": {"title": "口座情報の入力", "register_info": "口座情報を入力してください", "register_btn": "銀行口座を登録する", "account_name_title": "口座名義人名", "bank_name_title": "金融機関名", "bank_branch_title": "支店名（支店コード）", "account_number_title": "口座番号", "account_number_placeholder": "口座番号を入力してください", "deposit_type_title": "口座種別", "deposit_type": ["普通", "当座"]}}}, "regulation": {"regulation_omission": "〜中略〜", "term_of_service_title": "利用規約", "commercial_law_title": "特商法表記"}, "history": {"title": "りれき・有効期限", "moment_locate": "ja", "moment_weekdays_short": ["日", "月", "火", "水", "木", "金", "土"], "history_transaction_list": {"label_history_transaction": "履歴", "moment_format": "YYYY年M月D日（ddd） HH:mm", "history_transaction_expiry": "有効期限 ：", "history_empty": "履歴がありません", "button": "履歴"}, "history_expiry_list": {"label_expiry": "有効期限", "moment_format_no_time": "YYYY年M月D日（ddd）", "expiry_day_heading": "期限切れ日", "balance_heading": "残高", "button": "有効期限"}, "medal_history_list": {"transaction_id": "取引ID", "label_history_transaction": "履歴", "moment_format": "YYYY年M月D日（ddd） HH:mm", "history_transaction_expiry": "有効期限 ：", "history_empty": "りれきがありません", "button": "りれき", "show_more_item": "次の{0}件を表示"}, "medal_expiration_list": {"label_expiry": "有効期限", "purchase_date": "購入日", "moment_format_no_time": "YYYY年M月D日（ddd）", "expiry_day_heading": "期限切れ日", "history_empty": "りれきがありません", "balance_heading": "残高", "button": "有効期限", "label-amount": "金額", "label_date": "購入日"}}, "contact": {"option_select1": "新規登録について", "option_select2": "ログインについて", "option_select3": "申込について", "option_select4": "購入について", "option_select5": "支払いについて", "option_select6": "取扱店について", "option_select7": "利用環境について", "option_select8": "端末の操作について", "option_select9": "その他", "header": "お問い合わせ内容", "label_select": "お問い合わせ内容に一番近いものをお選びください", "placeholder_select": "選択してください", "placeholder_inquiry_detail": "ご入力をお願いします", "label_inquiry_detail": "詳細内容をご記入ください", "placeholder_email": "ご入力をお願いします", "label_email": "返信先メールアドレス", "button_submit": "送信", "dialog_title_top": "お問い合わせいただき", "dialog_title_bot": "ありがとうございます", "dialog_message": "担当者より折り返しご連絡いたしますので、少々お待ちください。", "text_inquiry": "500文字以内"}, "faq": {"title": "よくある質問", "introduce_login_heading": "ログインについて", "other_heading": "その他", "introduce_login_content": {"heading": "アカウントはどのように開設できますか。", "first_body1": "ハンバーガーメニューの", "mid_body1": "新規登録ボタン", "last_body1": "から新規会員登録を行ってください。", "body2": "新規会員登録にはメールアドレスとパスワード、電話番号の設定が必要です。"}, "other_content": {"panel2": {"heading": "パスワードが分からなくなってしまった。", "first_body1": "マイページ内ログイン画面の「", "mid_body1": "パスワードをお忘れの場合", "last_body1": "」からパスワードの変更ができます。", "body2": "初回登録がお済みでない場合は、初期画面に戻って「新規登録」から初回登録をしてください。"}, "panel3": {"heading": "コインの使用上限額はありますか。", "first_body1": "プレミアム分を含めた残高が5万円未満になるように制限しております。", "mid_body1": "お支払いの制限はございません", "last_body1": "。"}, "panel4": {"heading": " 星座と干支を忘れました。", "first_body1": "星座と干支をお忘れの場合", "mid_body1": "ハンバーガーメニューの「お問い合わせ」からお問い合わせください。"}}, "user_registration": {"heading": "ユーザー登録について", "child1": {"heading": "ユーザー登録はどのように行えばよいですか", "first_body1": "トップページの", "mid_body1": "ログイン/新規登録ボタン", "mid2_body1": "でログイン画面に遷移し、", "mid3_body1": "新規登録ボタン", "last_body1": "でユーザ登録を行ってください。", "body2": "メールアドレスとパスワード、携帯電話番号の設定が必要です。ユーザ登録時は「〇〇」申し込み時のメールアドレス、携帯電話番号でのみ登録が可能です。"}, "child2": {"heading": "入力した携帯電話番号にショートメール（SMS）が届かず、PINコードがわかりません。どうすればよいですか。"}}, "purchase_pay": {"heading": "〇〇の購入（チャージ）について"}, "payment_pay": {"heading": "「〇〇」での支払い・お買い物について"}, "usage_environment": {"heading": "利用環境について"}, "terminal_operation": {"heading": "端末の操作について"}, "contact": {"title1": "問題が解決しない場合は", "title2": "お問い合わせください", "contact": "お問い合わせ"}}, "login_open_id": {"heading-1": "各OpenIDの", "heading-2": "認証シーケンス", "btn-title": "ログイン"}, "menu": {"account_verified": "本人確認", "setting": "設定", "guest": "ゲストさん", "button": {"account_verify": "本人確認する", "sign_out": "ログアウト"}, "items": {"home": "トップ", "wallet": "ウォレット", "external_link": "外部リンク", "news": "お知らせ", "nft_market": "NFTマーケット", "language_select": "言語選択/Language", "terms_of_service": "利用規約", "commercial_law": "特商法表記", "faq": "よくある質問", "coupon": "クーポン", "event_list": "イベント", "event_form": "申込フォーム", "brand_link": "地域一覧", "store": "取扱店", "stamp_rally": "スタンプラリー", "survey": "アンケート", "mission": "ミッション", "logout": "ログアウト", "item": "アイテム", "ticket": "チケット", "collection": "コレクション", "card_payment": "カードからもらう"}, "confirmed": "マイナンバーカード 確認済", "unconfirmed": "マイナンバーカード確認 未完了", "btn-identity": "本人確認をする", "menu002": {"header_title": "言語選択", "label_ja": "日本語", "label_en": "英語"}}, "identification001": {"title": "本人確認を開始", "card_info": {"title": "マイナンバーカードをかざす", "description": "カードのICチップを読み取ることで本人確認を行います"}}, "header": {"news": "お知らせ", "menu": "メニュー", "qr_code": "QRコード"}, "footer": {"items": {"home": "トップ", "search": "さがす", "payment": "支払う", "news": "お知らせ", "menu": "メニュー", "wallet": "ウォレット", "collection": "コレクション", "community": "コミュニティ", "qrcode_read": "QR読取"}, "text": {"outOfPeriodTitle": "ご利用期間外です", "periodDetail": "ご利用可能期間:", "close_button": "とじる", "timeTile": "まで"}}, "news": {"news_for_you": "あなたへのお知らせ", "important_news": "重要なお知らせ", "loadmore": "次の{0}件を表示", "news_detail": "お知らせ詳細", "button": {"home": "ホームに戻る"}}, "coupon": {"screen_title": "クーポン一覧", "coupon_detail": {"title": "アイテム詳細", "dialog_confirm": {"activate_coupon_text": "クーポンを利用済にします。", "activate_coupon_question": "よろしいですか？", "operation_staff_text": "この操作はお店の人と", "confirm_again_text": "確認しながら進めてください", "execution_button": "実行", "cancel_button": "キャンセル"}, "to": "まで", "detail_name_coupon_label": "クーポン詳細", "store_name_label": "利用店舗", "end_date_time_label": "有効期限", "address_label": "住所", "go_map_button": "地図アプリで見る", "notice_label": "注意事項", "use_flag_label": " 利用状況", "used_coupon": " 利用済", "not_used_coupon": "未利用", "use_button": "利用する", "obtain_button": "入手する", "use_button_disable": "利用済み", "go_coupon_list_button": "クーポン一覧へ"}, "coupon004": {"screen_title": "クーポン確認", "title_dialog_success_used": "クーポンを利用済にしました", "title_dialog_success_got": "クーポンを入手済にしました", "title_checkbox": "この画面をお店の人に見せました", "ticket_advisory_confirmation": "この操作はお店の人と\n確認しながら進めてください"}, "available_coupon_tab": "利用可能", "used_coupon_tab": "利用済", "btn_search": "この内容で絞り込み", "date_use": "利用予定日", "title_search": "キーワード検索", "location": "場所（温泉地・都道府県）", "show_more_item": "次の{0}件を表示", "title_popup_search": "絞り込み", "place_holder_key_word": "キーワード", "start_date": "開始日", "end_date": "終了日", "used_coupon": "利用済", "text_btn_use": "利用する", "text_from": "まで", "text_use_any_times": "何回でも利用OK！", "text_no_coupon": "クーポンがありません。", "filter": {"dialog_title": "絞り込み", "headline": "利用状況", "btn_search": "絞り込み", "btn_reset": "リセット"}, "coupon001": {"label_key_word_search": "キーワード検索", "label_key_word_search_placeholder": "ガイド文字が入っているボタン", "label_sort": "並び替え", "newest": "新着順", "oldest": "古い順", "label_prefectures": "場所（都道府県)", "show_all_prefectures": "全ての都道府県を表示", "all": "すべて", "distribution_period": "配布期間", "use_possibility_period": "利用可能期間", "start_date": "開始日", "end_date": "終了日", "not_obtained": "未入手", "obtained": "入手済", "not_used": "未利用", "used": "利用済", "search": "さがす", "use_button": "利用する", "used_button": "利用済み"}}, "home": {"click_here_for_details": "詳細はこちら", "selected": "選択中", "view_details": "詳細を見る", "find_store": "お店を\nさがす", "deposit_save": "チャージ\n貯める", "charge_coins": "コインをチャージ", "pay_with_this": "これで\n支払う", "collection": "コレクション", "find_nft": "NFTを探す", "history": "履歴", "gift": "送る", "usage": "使い方", "open": "開く", "close": "閉じる", "campaign": "キャンペーン", "see_more": "もっと見る", "coupon": "所有クーポン", "event_ticket": "所有チケット", "coupon_use": "利用する", "balance": "残高", "balance_near_expire": "有効期限が近い残高があります", "to": "まで", "purchase_button_title": "購入する", "event_date": "開催日：", "title": "コミュニティ", "memberIcon": "会員証", "chatIcon": "チャット", "votingIcon": "投票", "jogging": "イベント", "chat": "話題の投稿", "ticket_type": {"paid": "チケットあり(有償)", "free": "チケットあり(無償)", "no_ticket": "チケットなし"}, "event_ticket_button": "利用する", "coupon_store_name": "取扱店", "coupon_end_date_time": "有効期限", "ticket_date_time": "開催時間", "text_from": "まで", "limited_store": "会員限定", "limited_store_intro_text": "あなただけの特別オファー！いつもとは違う限定メニューを楽しみましょう", "limited_store_use": "詳細", "text": {"outOfPeriodTitle": "ご利用期間外です", "periodDetail": "ご利用可能期間:", "close_button": "とじる", "timeTile": "まで"}}, "usage": {"title_header_charging": "入金するには", "title_header_payment": "お支払いの方法", "title_header_history": "りれきを確認する"}, "survey": {"screen_name": "アンケート一覧", "reception_period": "受付期間：", "to": "まで", "no_data": "現在アンケートは実施しておりません", "point_text": "ポイント", "not_applicable_text": "対象外", "answer_button": {"answer": "回答する", "answered": "回答済"}, "survey002": {"screen_name": "アンケート入力", "input_placeholder": "その他を選択した方は詳細をご記入ください", "prefectures_select_box": "確認画面へ", "municipalities_info": "キャンセル", "has_ended": "は終了しました"}, "multiple_select": "※ 複数選択可", "select_up_to": "※ 最大{0}個まで選択可能です", "select_equal": "※ {0}個選択してください", "select_in_order": "※ 優先順位順に選択してください。", "clear": "クリア", "survey003": {"header": "アンケート確認", "heading": "入力に間違いがないか、ご確認ください", "fix_btn": "キャンセル", "send_btn": "送信", "warning_text": "個人情報の取り扱いに同意いただけない場合、入力内容を送信できません。", "terms_button": "個人情報の取り扱いについて", "terms_check": "個人情報の取り扱いに同意する"}, "survey004": {"header": "アンケート完了", "text": "回答を受け付けました", "text_2": "ポイント獲得しました", "button": "とじる", "back_to_home": "ウォレットに戻る", "survey_end": "アンケート受付期間は\n終了しました", "reception_period": "受付期間", "back_to_survey_list": "アンケート一覧に戻る", "of": "を"}}, "paymulti": {"title_header_scan": "支払い", "title_header_confirm": "支払い内容確認", "title_header_complete": "支払い完了", "title_confirm": "お支払い内容をご確認ください", "gift_certificates": "使用する商品券・ポイント", "store_name": "店舗名", "payment_amount": "支払い金額", "title_checkbox": "この画面をお店の人に見せました", "confirm_checkbox": "この内容で支払う", "payment_complete": "支払いが完了しました", "return_home": "ホームに戻る", "determine_payment_amount": "支払い金額を決める", "at_store": "この店舗で", "available_balance": "利用できる残高", "not_found_store": "店舗アドレス不正です", "store_is_incorrect": "店舗が違います", "amount_error": "残高以内の金額を入力ください。", "store_to_pay": "支払う店舗を指定します", "store_address_not_exist": "入力された店舗アドレスは存在しません", "unit": "円", "place_holder_input_store": "店舗アドレスを入力してください", "label_input_store": "店舗アドレス(半角数字10桁)", "sub_label_input_store": "店舗アドレスは各店舗でご確認ください", "place_holder_payment": "支払い金額を入力してください", "label_payment": "支払い金額", "button_back": "戻る", "button_next": "次へ", "balance": "残高", "use": "利用", "gift_certificates_point": "使える対象", "what_to_use": "使用する対象", "remaining_payment_amount": "支払い金額のこり", "home_btn": "ホームに戻る", "enter_half_with_number": "{0}ケタの半角数字を入力してください。", "may_already_completed": "既に決済が完了している可能性があります。一度ホーム画面に戻り、ご利用履歴をご確認ください。"}, "charge_vouchr": {"charge_vouchr001": {"header_title": "商品券購入", "purchase_info": "購入数を指定", "done_button": "決定", "required_text": "必須", "product_volume": "円 / 1セット", "product_premium_value": "円分", "product_unit": "セット"}, "charge_vouchr002": {"header_title": "購入方法の選択", "chage_info": "購入する方法を選択してください。", "settlement_agency_info": "提携先の株式会社DGフィナンシャルテクノロジーの決済画面が表示されますので、引き続き決済手続きを進めてください。", "credit_card_info_header": "クレジットカード", "credit_card_info_content": "指定した金額をクレジットカードからチャージします", "credit_card_done_button": "クレジットカードでチャージ"}, "charge_vouchr003": {"header_title": "購入完了", "description_title": "購入しました", "date_title": "有効期限："}}, "ticket": {"ticket001": {"screen_title": "所持チケット一覧", "available_button": "利用可能", "already_used_button": "利用済", "event_display_button": "次の5件を表示", "free": "無料", "text_no_ticket": "クーポンがありません。", "price_unit": "円", "no_date_time_specified": "開催日なし　時間指定なし", "no_date_specified": "開催日なし ", "no_time_specified": "時間指定なし"}, "ticket002": {"screen_name": "チケット詳細", "event_ticket_price": {"unit": "円", "free": "無料"}, "date_headline": "日付", "time_headline": "時間", "inquiry_headline": "お問い合わせ", "notes_headline": "注意事項", "purchase_date_headline": {"free": "入手日", "charge": "購入日"}, "reserve_id_headline": "予約番号", "expiration_headline": "有効期限", "usage_headline": "利用状況", "unused": "未利用", "used": " 利用済", "usedButton": "利用済", "useButton": "利用する", "ticket_list_button": "イベント詳細"}, "ticket003": {"screen_name": "チケット確認", "ticket_use_confirmation": "チケットを利用済にします。\nよろしいですか？", "ticket_advisory_confirmation": "この操作はお店の人と\n確認しながら進めてください", "execution_button": "実行", "cancel_button": "キャンセル", "price_unit": "円", "reserve_id": "予約番号", "useButton": "利用する", "title_checkbox": "この画面を係員に見せました"}, "ticket004": {"ticket_changed": "チケットを利用済にしました"}}, "stamp_rally": {"stamp_rally001": {"screen_name": "スタンプラリー一覧", "show_more_item": "次の{0}件を表示", "data_stamp_rally_not_found": "スタンプラリーがありません", "stamp_rally_refinement": {"all": "すべて", "in_session": "開催中", "before_session": "開催前"}, "situation_info": "達成", "filter": {"dialog_title": "絞り込み", "text_search": "絞り込み", "headline": "開催状況", "btn_search": "絞り込み", "btn_cancel": "リセット"}}, "stamp_rally002": {"error_get_location": "位置情報の取得に失敗しました。", "error_permission_location": "位置情報の利用が許可されていません。", "mission": {"achieved": "達成済", "not_achieved": "未達成"}, "reward": {"obtained": "入手済", "not_obtained": "未入手"}, "clear_rule": {"not_written": "表記しない", "achieved_mission": "SPOT {0}つを達成", "scan_qr": "現地でQRコードを読み込む", "purchase_qr": "現地でQRコードを読み込み購入", "clear": "ピンの範囲にいればクリア", "achieved_all": "すべてのスタンプを達成", "load_qr": "QRコードを読み込む"}, "display": {"map": "地図表示にする", "list": "リスト表示にする"}, "qr_button": " QRコード 読込", "location_button": " 現在地", "date_time": "{0}まで"}, "stamp_rally003": {"incentive_header": "達成報酬"}, "rally004": {"screen_name": "QRコード読込", "info_text": "カメラをQRコードに向けてください", "qr_unreadable_info": "QRコードが読み取れない", "scan_error": "コードが不正です"}, "rally005": {"screen_name": "スポットコード", "heading": "スポットコードを入力", "code_info": "スポットコードはQRコードの下にある10ケタの数字です。", "code_input_placeholder": "#スポットコードを入力してください", "error_message": "{0}ケタの半角数字を入力してください。"}, "rally007": {"message_success": "スタンプを押しました"}, "stamp_rally006": {"screen_title": "スポット詳細", "mission": {"achieved": "達成済", "not_achieved": "未達成"}, "reward": {"obtained": "入手済", "not_obtained": "未入手"}, "to": "まで", "stamp_detail_title": "スタンプ詳細", "contact_title": "連絡先", "address_title": "アクセス", "notes_title": "注意事項", "incentive_title": "達成報酬", "name_qr_code_btn1": "入手する", "name_qr_code_btn2": "QRコード読込", "name_qr_code_btn3": "MISSIONをすべてクリアしたら解放", "name_qr_code_btn4": "達成済み", "name_qr_code_btn5": "コレクション", "name_qr_code_btn6": "期間外です", "back_button": "戻る", "mission_complete_date": "達成日：{0}", "get_reward_date": "入手日：{0}"}, "stamp_rally007": {"guidance_coupon": "クーポンをゲットしました", "guidance_coin": "コインをゲットしました", "guidance_nft": "NFTをゲットしました", "guidance_stamp": "スタンプをゲットしました", "nft_info1": "発効までに数分かかる場合がございます", "nft_info2": "発行したNFTは", "nft_info3": "コレクション", "nft_info4": "から確認可能です"}}, "paysingle001-main-search-storepubliccode-001-ERROR": "リクエストパラメータが異常です。", "paysingle001-main-search-storepubliccode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paysingle001-main-search-storepubliccode-003-ERROR": "店舗アドレスが不正です。", "paysingle001-main-search-storepubliccode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paysingle001-main-search-storepubliccode-005-ERROR": "取り扱いのない店舗です。", "paysingle004-main-execute-payment-001-ERROR": "リクエストパラメータが異常です。", "paysingle004-main-execute-payment-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paysingle004-main-execute-payment-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paysingle004-main-execute-payment-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paysingle004-main-execute-payment-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paysingle004-main-execute-payment-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paysingle004-main-execute-payment-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paysingle002-main-search-storepubliccode-001-ERROR": "リクエストパラメータが異常です。", "paysingle002-main-search-storepubliccode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paysingle002-main-search-storepubliccode-003-ERROR": "入力された店舗アドレスは存在しません", "paysingle002-main-search-storepubliccode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paysingle002-main-search-storepubliccode-005-ERROR": "取り扱いのない店舗です。", "paymulti005-main-execute-payment-001-ERROR": "リクエストパラメータが異常です。", "paymulti005-main-execute-payment-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paymulti005-main-execute-payment-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paymulti005-main-execute-payment-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paymulti005-main-execute-payment-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paymulti005-main-execute-payment-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paymulti005-main-execute-payment-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paymulti005-main-execute-payment-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paymulti005-main-execute-payment-009-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paymulti005-main-execute-payment-010-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paymulti003-init-get-availablecoinid-001-ERROR": "リクエストパラメータが異常です。", "paymulti003-init-get-availablecoinid-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "paymulti003-init-get-availablecoinid-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "paymulti002-main-search-storepubliccode-001-ERROR": "リクエストパラメータが異常です。", "paymulti002-main-search-storepubliccode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paymulti002-main-search-storepubliccode-003-ERROR": "店舗アドレスが不正です。", "paymulti002-main-search-storepubliccode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paymulti001-main-search-storepubliccode-001-ERROR": "リクエストパラメータが異常です。", "paymulti001-main-search-storepubliccode-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "paymulti001-main-search-storepubliccode-003-ERROR": "店舗アドレスが不正です。", "paymulti001-main-search-storepubliccode-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "history001-init-get-history-list-001-ERROR": "リクエストパラメータが異常です。", "history001-init-get-history-list-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "history001-init-get-history-list-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "history001-init-get-history-list-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargevouchr002-main-execute-charge-001-ERROR": "リクエストパラメータが異常です。", "chargevouchr002-main-execute-charge-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargevouchr002-init-get-clientkey-001-ERROR": "リクエストパラメータが異常です。", "chargevouchr002-init-get-clientkey-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "chargeselect001-init-get-clientkey-001-ERROR": "リクエストパラメータが異常です。", "chargeselect001-init-get-clientkey-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "news001-init-get-important-news-unread-flag-001-ERROR": "システムエラーが発生しました。", "news001-init-get-important-news-unread-flag-002-ERROR": "システムエラーが発生しました。", "news001-init-get-personal-news-unread-flag-001-ERROR": "システムエラーが発生しました。", "news001-init-get-personal-news-unread-flag-002-ERROR": "システムエラーが発生しました。", "ticket001-init-get-event-ticket-list-001-INFO": "", "ticket001-init-get-event-ticket-list-002-ERROR": "システムエラーが発生しました。", "ticket001-init-get-event-ticket-list-003-ERROR": "システムエラーが発生しました。", "ticket001-init-get-event-ticket-list-004-ERROR": "システムエラーが発生しました。", "ticket001-init-get-event-ticket-list-005-ERROR": "システムエラーが発生しました。", "ticket001-init-get-event-ticket-list-006-ERROR": "システムエラーが発生しました。", "event": {"screen_title": "イベント一覧", "show_more_item": "次の{0}件を表示", "search_button": "検索", "search_placeholder": "検索・絞り込み", "squeeze_search_Button": "絞り込み", "ticket_link": "所持チケット", "data_not_found": "イベントがありません", "search_event": "「{0}」の検索結果{1}件", "event_002": {"title": "絞り込み", "search_label": "キーワード検索", "search_placeholder": "キーワード", "rearrange_label": "並べ替え", "location_label": "場所(都道府県)", "location_placeholder": "すべて", "event_date_label": "開催期間", "reset_filter_button": "リセット", "filter_button": "絞り込み", "start_date": "開始日", "end_date": "終了日"}, "event_003": {"title": "イベント詳細", "ticket_sale_term_headline": "チケット販売期間", "ticket_sale_term_empty": "販売期間なし", "precautions_message": "※事前にチケットの購入が必要です。", "precautions_free_message": "※事前にチケットの入手が必要です。", "out_of_period_date": "チケット販売期間外", "buy_ticket": "入場チケットを購入", "ticket_out_of_stock": "チケットは売り切れました", "ticket_free": "チケットを入手", "sns_headline": "イベント公式SNS", "hp_headline": "イベント公式HP", "event_detail_headline": "イベント詳細", "open_date_headline": "開催日", "open_google_map": "地図アプリで見る", "train_headline": "電車・バスで", "car_headline": "お車で", "ticket_headline": "チケット説明", "precautions_headline": "注意事項", "location_title": "アクセス", "ticket_buy_free": "チケット入手", "ticket_buy_paid": "入場チケット購入", "status_Clear": "売り切れ", "status_ChangeHistory": "残りわずか", "status_PanoramaFishEye": "販売中", "no_time": "時間指定なし", "free_price": "無料", "total": "合計", "sheet_headline": "枚数", "sheet_unit": "枚", "ticket_free_button": "入手", "ticket_paid_button": "購入", "message_check_balance": "残高が不足しています", "message_ticket_sale_check": "残り{0}枚です。\n{0}枚以上は{1}できません。", "message_max_buy_check": "一度に{0}できる枚数は{1}枚までです。", "questionnaire": "アンケート", "survey_form": {"screen_title": "申込フォーム", "mess_success": "申込フォームを受け付けました", "mess_fail": "こちらの申込フォームは終了しました", "reception_period": "受付期間：", "to": "まで", "input_placeholder": "その他を選択した方は詳細をご記入ください", "prefectures_select_box": "確認画面へ", "municipalities_info": "キャンセル", "free_text": "自由記述欄", "max_choices": "選択数が不足しています。{0}個選択してください。", "max_length_error_message": "入力は{0}文字以内で入力してください。", "up_to": "{0}文字まで"}}, "event_004": {"title": "支払い", "title_content": "支払い金額のこり", "back_button": "戻る", "next_button": "次へ", "close_button": "とじる", "back_home_button": "ホーム", "error_message": "残高が足りません", "hint_message": "入金してください", "payment_amount_remaining": "支払い金額のこり", "list_payment_item": "使える対象"}, "event_005": {"title_free": "内容確認", "title_paid": "支払い内容確認", "paid_headline": "お支払い内容をご確認ください", "free_headline": "内容をご確認ください", "ticket_free_button": "入手", "ticket_paid_button": "購入", "cancel_button": "キャンセル", "date_headline": "日付", "time_headline": "時間", "sheet_headline": "枚数", "payment_date_headline": "支払日時", "paymen_coin_deadline": "使用する対象", "payment_amount_headline": "支払い金額", "survey_headline": "アンケート回答内容", "ticket": "チケット", "point": "ポイント", "processing_text": "処理を行っています"}, "event_006": {"title_free": "入手完了", "title_paid": "購入完了", "free_text": "入手", "paid_text": "購入", "payment_completed_info_free": "チケットを \n入手しました", "payment_completed_info_paid": "チケットを \n購入しました", "back_home_button": "ホームに戻る", "back_ticket_list_button": "所持チケット一覧", "ticket_bought_text": "チケットを{0}しました", "confirm_bought_text1": "{0}したチケットは", "confirm_bought_text2": "アイテム", "confirm_bought_text3": "から確認できます"}}, "charge_qr_code_present_001": {"header_title": "QRコード表示", "transfer_code_title": "[QRコードナンバー]"}, "event_form002": {"screen_title": "申込フォーム", "mess_success": "申込フォームを受け付けました", "mess_fail": "こちらの申込フォームは終了しました", "reception_period": "受付期間：", "to": "まで", "input_placeholder": "その他を選択した方は詳細をご記入ください", "prefectures_select_box": "確認画面へ", "municipalities_info": "キャンセル", "free_text": "自由記述欄", "max_choices": "選択数が不足しています。{0}個選択してください。"}, "event_form": {"event_form002": {"confirmation": "入力に間違いがないか、ご確認ください", "screen_title": "入力内容の確認", "fix_btn": "入力内容を修正", "send_btn": "送信", "max_length_error_message": "入力は{0}文字以内で入力してください。"}, "event_form003": {"text": "イベント申込を受け付けました", "button": "とじる"}, "event_form001": {"screen_title": "申込フォーム", "show_more_item": "次の{0}件を表示", "data_event_form_not_found": "申込フォームがありません", "event_form_refinement": {"all": "すべて", "in_session": "受付中", "before_session": "受付前"}}}, "mission": {"mission001": {"screen_name": "ミッション一覧", "show_more_item": "次の{0}件を表示", "data_mission_not_found": "ミッションがありません", "situation_info": "達成", "search_title": "絞り込み", "search_title_1": "開催状況", "reset_btn": "リセット", "search_btn": "絞り込み", "search_checkbox_option_1": "開催中", "search_checkbox_option_2": "開催前", "search_checkbox_option_3": "開催終了"}, "mission002": {"show_more_item": "次の{0}件を表示", "data_mission_not_found": "ミッションがありません", "situation_info": "達成", "incentive_title": "達成報酬", "reward": {"achieved": "達成済", "unachieved": "未達成"}, "conditiontype": {"login": "ログイン", "balance": "チャージ", "payment": "支払い", "event": "イベント", "qrcode": "QRコードを読み込む", "qrcodeocation": "現地でGPSかQRコードを読み込む"}, "mission_key": "MISSION", "check_point_key": "CHECK POINT", "name_qr_code_btn1": "利用する", "name_qr_code_btn2": "獲得済", "name_qr_code_btn3": "MISSIONをすべてクリアしたら解放", "current_status": "現在のステータス", "mission_list": "ミッション一覧", "mission_tips": "ミッション達成のコツ"}, "mission003": {"screen_title": "ミッション詳細", "reward": {"obtained": "達成済", "not_obtained": "未達成"}, "to": "まで", "stamp_detail_title": "スタンプ詳細", "contact_title": "連絡先", "address_title": "住所", "notes_title": "注意事項", "incentive_title": "達成報酬", "name_qr_code_btn1": "GPSを読み込んで達成", "name_qr_code_btn2": "QRコードを読み込んで達成", "name_qr_code_btn3": "利用する", "name_qr_code_btn4": "達成済み", "back_button": "戻る", "mission_complete_date": "達成日：{0}", "get_reward_date": "入手日：{0}", "message_success": "ミッションを達成しました", "time": "回"}, "mission004": {"screen_name": "QRコード読込", "message_success": "ミッションを達成しました"}, "mission005": {"screen_name": "ミッションコード", "heading": "ミッションコードを入力", "code_info": "ミッションコードはQRコードの下にある10ケタの英数字です", "code_input_placeholder": "ミッションコードを入力してください", "message_success": "ミッションを達成しました"}, "mission006": {"message_success": "読み取りが完了しました。", "message_danger": "※達成状況は翌日以降に反映されます"}, "mess_coupon_success": "クーポンを獲得しました", "mess_coin_success": "コインを獲得しました"}, "ec": {"ec_item001": {"title": "商品一覧", "cart": "買い物かご", "purchase_history": "購入履歴", "search_placeholder": "何をお探しですか？", "next_button_text": "次の{0}件を表示", "no_result": "商品がありません。", "tax_label": "（税込）"}, "ec_item002": {"header": "商品詳細", "shopping_cart": "買い物かご", "purchase_history": "購入履歴", "quantity": "数量", "item_description": "商品説明", "shop_info": "ショップ情報", "shop_name": "ショップ名", "business_hours": "営業時間", "access": "アクセス", "view_with_map_app": "地図アプリで見る", "add_to_cart": "買い物かごに追加", "go_to_cart": "買い物かごへ進む", "tax_included": "（税込）", "added_to_shopping_cart": "買い物かご追加済み", "cannot_add_more": "これ以上追加できません", "cannot_proceed": "決済処理中のため、 買い物かごに追加できません。", "product_limit_error": "商品数が上限を超えているため\n買い物かごに追加できませんでした。", "product_addition_error": "注文個数が購入可能数を超えているため\n買い物かごに追加できませんでした。", "add_product_success": "商品を買い物かごに追加しました。", "out_of_stock": "在庫切れ", "return_to_shopping_top_page": "買い物トップページへ戻る"}, "ec_cart001": {"header": "買い物かご", "quantity": "数量", "product_is_currently_unavailable_for_purchase": "この商品は現在購入できません", "quantity_exceeds_stock": "数量が在庫数を超えています", "please_check_product_details": "商品詳細を確認してください", "go_to_product_details": "商品詳細へ", "delete": "削除する", "not_complete_successfully": "前回の決済処理が正常に終了しませんでした。", "subtotal": "小計", "merchandise": "商品", "unit": "円", "proceed_to_purchase_procedure": "購入手続きへ進む", "return_to_shopping_top_page": "買い物トップページへ戻る", "no_products": "買い物かごに商品が入っていません", "tax_label": "（税込）"}, "item_status": {"has_deleted": "この商品は削除されています", "currently_unavailable_for_purchase": "この商品は現在購入できません", "not_yet_available_for_sale": "この商品は販売開始前です", "no_longer_available_for_sale": "この商品は販売が終了しています"}, "ec_payment001": {"header": "お支払方法の選択", "step": [{"text": "買い物かご"}, {"text": "購入手続き"}, {"text": "お支払方法"}, {"text": "購入完了"}], "title1": "お支払方法の確認", "title2": "※購入はまだ確定していません", "credit_label1": "クレジットカード", "credit_label2": "クレジットカードで所定の手続きを行い\nお支払い手続きを進めます", "button_credit": "クレジットカードで支払う", "payment_processing": "決済処理中のため、処理できません。\nしばらく時間をおいてから実行して下さい。", "payment_update_datetime": "買い物かごの内容が変更されています。\n買い物かごを確認して下さい。", "payment_product_update": "情報が更新されている商品があります。\n商品情報を確認して下さい。", "purchase_failed": "取引が失敗しました。", "top_button": "買い物トップページへ戻る"}, "ec_purchase001": {"title": "購入手続き", "bread_crumb_list": [{"text": "買い物がご"}, {"text": "購入手続き"}, {"text": "お支払方法"}, {"text": "購入完了"}], "content_confirm_label1": "購入内容の確認", "content_confirm_label2": "※購入はまだ確定していません", "pickup_date_time_label": "受け取り日時", "pickup_location_label": "受け取り場所", "pickup_date_time": "受け取り日時が決まり次第、メールでご案内いたします", "payment_select_button": "お支払方法を選択する", "top_button": "買い物トップページへ戻る", "user_info_label": "注文者情報", "last_name_placeholder": "姓", "first_name_placeholder": "名", "required_text": "必須", "name_label": "氏名", "phone_no_label": "電話番号", "sub_phone_no_label": "例：***********", "phone_no_placeholder": "携帯番号を入力してください", "quantity_label": "数量", "total_count_label": "小計 ({0}商品)", "tax_label": "（税込）"}, "ec_purchase002": {"title": "購入履歴", "purchase_date_time_label": "購入日：", "next_button_text": "次の{0}件を表示", "no_result": "購入履歴がありません。", "return_to_shopping_top_page": "買い物トップページへ戻る"}, "ec_purchase003": {"title": "購入完了", "your_purchase_is_complete": "購入が完了しました", "purchase_information": "※購入情報は\n購入履歴からご確認いただけます", "back_to_top_page": "買い物トップページに戻る"}, "ec_purchase004": {"title": "購入エラー", "error_has_occurred": "エラーが発生したため\n購入が完了していません", "purchase_information": "もう一度、購入手続きを行ってください", "back_to_top_page": "買い物トップページに戻る"}}, "transfer": {"transfer001": {"title": "送る", "transfer_amount_label": "金額", "medal_service_label": "使用する対象", "input": {"over_current_balance": "残高以内の金額を入力ください。", "balance": "残高"}, "qr_code_button": "QRコード読取"}, "transfer002": {"title": "送る相手のQRコードを読み取ってください", "mess_qr_invalid": "QRコードが不正です"}, "transfer003": {"header": "確認", "transfer_amount_label": "金額", "name_label": "送る相手", "medal_service_label": "使用する対象", "balance": "残高", "pay_btn_label": "送る", "home_btn": "ホームに戻る", "may_already_completed": "既に決済が完了している可能性があります。一度ホーム画面に戻り、ご利用履歴をご確認ください。"}, "transfer004": {"title": "完了", "name": "さんへ", "transfer_amount": "円　送りました。", "btn": "ホームに戻る"}, "transfer005": {"title": "もらう", "info_test_line1": "もらう相手のカードをお手元に準備してください。", "info_test_line2": "QRコード読取ボタンを押下して、\nカードのQRコードを読み取ってください。", "qr_code_button": "QRコード読取"}, "transfer006": {"title": "カード読み取り", "info_test": "もらう相手のカードのQRコードを読み取ってください", "qr_code_invalid": "QRコードが不正です", "qr_code_not_allow": "カードのQRコードを読み取らせてください"}, "transfer007": {"title": "もらう", "transfer_amount_label": "金額", "name_label": "もらう相手", "medal_service_label": "使用する対象", "input_error_message": "残高以内の金額を入力ください", "next_button": "次へ", "input": {"over_current_balance": "残高以内の金額を入力ください。"}}, "transfer008": {"title": "確認", "transfer_amount_label": "金額", "name_label": "もらう相手", "medal_service_label": "使用する対象", "pay_button": "もらう", "home_btn": "ホームに戻る", "may_already_completed": "既に決済が完了している可能性があります。一度ホーム画面に戻り、ご利用履歴をご確認ください。"}, "transfer009": {"title": "完了", "name": "さんから", "transfer_amount": "円　もらいました。", "btn": "ホームに戻る"}}, "event001-init-get-event-ticket-list-001-INFO": "", "event001-init-get-event-ticket-list-002-ERROR": "システムエラーが発生しました。", "event001-init-get-event-ticket-list-003-ERROR": "システムエラーが発生しました。", "event001-init-get-event-ticket-list-004-ERROR": "システムエラーが発生しました。", "event001-init-get-event-ticket-list-005-ERROR": "システムエラーが発生しました。", "event001-init-get-event-ticket-list-006-ERROR": "システムエラーが発生しました。", "event001-init-get-evevnt-ticket-query-001-ERROR": "システムエラーが発生しました。", "event001-init-get-evevnt-ticket-query-002-ERROR": "システムエラーが発生しました。", "event003-init-get-availablecoinid-001-ERROR": "リクエストパラメータが異常です。", "event003-init-get-availablecoinid-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "event003-init-get-availablecoinid-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "event003-init-get-event-ticket-detail-001-INFO": "", "event003-init-get-event-ticket-detail-002-ERROR": "システムエラーが発生しました。", "event003-init-get-event-ticket-detail-003-ERROR": "システムエラーが発生しました。", "event003-init-get-event-ticket-detail-004-ERROR": "システムエラーが発生しました。", "event003-init-get-event-ticket-detail-005-ERROR": "システムエラーが発生しました。", "event003-init-get-event-ticket-detail-006-ERROR": "システムエラーが発生しました。", "event003-init-get-event-ticket-detail-007-ERROR": "システムエラーが発生しました。", "event003-init-get-event-ticket-detail-008-ERROR": "システムエラーが発生しました。", "event003-init-get-event-ticket-detail-009-ERROR": "システムエラーが発生しました。", "event003-init-get-event-ticket-detail-010-ERROR": "システムエラーが発生しました。", "event003-init-get-event-ticket-detail-011-ERROR": "システムエラーが発生しました。", "event003-init-get-event-ticket-detail-012-ERROR": "システムエラーが発生しました。", "event005-main-wallet-cash-transfer-001-ERROR": "リクエストパラメータが異常です。", "event005-main-wallet-cash-transfer-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "event005-main-wallet-cash-transfer-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "event005-main-wallet-cash-transfer-004-ERROR": "システムエラーが発生しました。チケットの購入に失敗しました。", "event005-main-wallet-cash-transfer-005-ERROR": "システムエラーが発生しました。チケットの購入に失敗しました。", "event005-main-wallet-cash-transfer-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "event005-main-wallet-cash-transfer-007-ERROR": "残り{0}枚です。\n{1}枚以上は購入できません。", "event005-main-wallet-cash-transfer-008-ERROR": "一度に購入できる枚数は{0}枚までです。", "event005-main-wallet-cash-transfer-009-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "event005-main-wallet-cash-transfer-010-ERROR": "残高が不足しています。", "event005-main-wallet-cash-transfer-011-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "event005-main-wallet-cash-transfer-012-ERROR": "システムエラーが発生しました。ユーザーイベントチケットリストの登録に失敗しました。UserEventTicketID：{0}", "event005-main-wallet-cash-transfer-013-ERROR": "システムエラーが発生しました。イベントチケット詳細リストの更新に失敗しました。EventTicketDetailID：{0}", "event005-main-wallet-cash-transfer-014-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "ticket002-init-get-event-ticket-detail-002-ERROR": "システムエラーが発生しました。", "ticket002-init-get-event-ticket-detail-003-ERROR": "システムエラーが発生しました。", "ticket002-init-get-event-ticket-detail-004-ERROR": "システムエラーが発生しました。", "ticket002-init-get-event-ticket-detail-005-ERROR": "システムエラーが発生しました。", "ticket002-init-get-event-ticket-detail-006-ERROR": "システムエラーが発生しました。", "ticket003-main-use-event-ticket-001-INFO": "", "ticket003-main-use-event-ticket-002-ERROR": "システムエラーが発生しました。", "ticket003-main-use-event-ticket-003-ERROR": "システムエラーが発生しました。", "ticket003-main-use-event-ticket-004-WARNING": "このチケットは既に利用済みです。", "ticket003-main-use-event-ticket-005-ERROR": "システムエラーが発生しました。", "ticket003-main-use-event-ticket-006-ERROR": "システムエラーが発生しました。", "stamprally006-main-search-stamprally-002-ERROR": "リクエストパラメーターが異常です。", "stamprally006-main-search-stamprally-003-ERROR": "データの取得に失敗しました。", "stamprally006-main-search-stamprally-004-ERROR": "コードが読み込めませんでした。", "stamprally006-main-search-stamprally-005-ERROR": "データの取得に失敗しました。", "stamprally006-main-search-stamprally-006-ERROR": "データの取得に失敗しました。", "stamprally006-main-search-stamprally-007-WARNING": "このユーザーは既にミッションをクリアしています。", "stamprally006-main-search-stamprally-008-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-009-WARNING": "このユーザーは既にミッションをクリアしています。", "stamprally006-main-search-stamprally-010-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-011-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-012-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-013-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-014-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-015-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-016-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-017-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-018-ERROR": "common-wallet-chargeの実行に失敗しました。", "stamprally006-main-search-stamprally-019-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-020-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-021-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-022-ERROR": "購入上限を超えています。", "stamprally006-main-search-stamprally-023-ERROR": "販売期間外です。", "stamprally006-main-search-stamprally-024-ERROR": "在庫がありません。", "stamprally006-main-search-stamprally-025-ERROR": "シリアルコードを取得できませんでした。", "stamprally006-main-search-stamprally-026-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-027-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-028-ERROR": "common-wallet-paymentの実行に失敗しました。", "stamprally006-main-search-stamprally-029-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-030-WARNING": "このユーザーは既にミッションをクリアしています。", "stamprally006-main-search-stamprally-031-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-032-WARNING": "ミッションのピンの位置と離れています。", "stamprally006-main-search-stamprally-033-ERROR": "システムエラーが発生しました。", "stamprally006-main-search-stamprally-034-ERROR": "システムエラーが発生しました。", "stamprally006-main-search-stamprally-035-ERROR": "データの取得に失敗しました。", "stamprally006-main-search-stamprally-036-ERROR": "データの取得に失敗しました。", "stamprally006-main-search-stamprally-037-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-038-ERROR": "データの登録に失敗しました。", "stamprally006-main-search-stamprally-039-ERROR": "データの登録に失敗しました。", "stamprally006-main-search-stamprally-040-ERROR": "データの取得に失敗しました。", "stamprally006-main-search-stamprally-041-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-042-ERROR": "common-wallet-chargeの実行に失敗しました。", "stamprally006-main-search-stamprally-043-ERROR": "データの登録に失敗しました。", "stamprally006-main-search-stamprally-044-ERROR": "データの取得に失敗しました。", "stamprally006-main-search-stamprally-045-ERROR": "データの取得に失敗しました。", "stamprally006-main-search-stamprally-046-ERROR": "購入上限を超えています。", "stamprally006-main-search-stamprally-047-ERROR": "販売期間外です。", "stamprally006-main-search-stamprally-048-ERROR": "在庫がありません。", "stamprally006-main-search-stamprally-049-ERROR": "シリアルコードを取得できませんでした。", "stamprally006-main-search-stamprally-050-ERROR": "データの更新に失敗しました。", "stamprally006-main-search-stamprally-051-ERROR": "データの取得に失敗しました。", "stamprally006-main-search-stamprally-052-ERROR": "common-wallet-paymentの実行に失敗しました。", "stamprally006-main-search-stamprally-053-ERROR": "データの登録に失敗しました。", "stamprally006-main-search-stamprally-054-ERROR": "データの登録に失敗しました。", "stamprally006-main-search-stamprally-055-ERROR": "不正なコードです。", "stamprally006-main-search-stamprally-056-ERROR": "データの登録に失敗しました。", "stamprally006-main-search-stamprally-057-ERROR": "データの登録に失敗しました。", "stamprally006-main-search-stamprally-058-ERROR": "期間外のスタンプラリーです。", "stamprally006-main-search-stamprally-059-ERROR": "非公開のスタンプラリーです。", "stamprally004-init-get-stamprally-mission-list-002-ERROR": "システムエラーが発生しました。", "stamprally004-init-get-stamprally-mission-list-003-ERROR": "システムエラーが発生しました。", "stamprally004-init-get-stamprally-mission-list-004-ERROR": "システムエラーが発生しました。", "stamprally004-init-get-stamprally-mission-list-005-ERROR": "システムエラーが発生しました。", "stamprally004-init-get-stamprally-mission-list-006-ERROR": "システムエラーが発生しました。", "stamprally004-init-get-stamprally-mission-list-007-ERROR": "システムエラーが発生しました。", "stamprally004-init-get-stamprally-mission-list-008-ERROR": "システムエラーが発生しました。", "stamprally004-init-get-stamprally-mission-list-009-ERROR": "システムエラーが発生しました。", "stamprally004-init-get-stamprally-mission-list-010-ERROR": "システムエラーが発生しました。", "stamprally005-init-update-stamprally-002-ERROR": "リクエストパラメーターが異常です。", "stamprally005-init-update-stamprally-003-ERROR": "データの取得に失敗しました。", "stamprally005-init-update-stamprally-004-ERROR": "データの取得に失敗しました。", "stamprally005-init-update-stamprally-005-WARNING": "このユーザーは既にミッションをクリアしています。", "stamprally005-init-update-stamprally-006-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-007-WARNING": "このユーザーは既にミッションをクリアしています。", "stamprally005-init-update-stamprally-008-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-009-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-010-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-011-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-012-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-013-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-014-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-015-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-016-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-017-ERROR": "common-wallet-chargeの実行に失敗しました。", "stamprally005-init-update-stamprally-018-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-019-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-020-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-021-ERROR": "購入上限を超えています。", "stamprally005-init-update-stamprally-022-ERROR": "販売期間外です。", "stamprally005-init-update-stamprally-023-ERROR": "在庫がありません。", "stamprally005-init-update-stamprally-024-ERROR": "シリアルコードを取得できませんでした。", "stamprally005-init-update-stamprally-025-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-026-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-027-ERROR": "common-wallet-paymentの実行に失敗しました。", "stamprally005-init-update-stamprally-028-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-029-WARNING": "このユーザーは既にミッションをクリアしています。", "stamprally005-init-update-stamprally-030-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-031-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-032-WARNING": "ミッションのピンの位置と離れています。", "stamprally005-init-update-stamprally-033-ERROR": "システムエラーが発生しました。", "stamprally005-init-update-stamprally-034-ERROR": "システムエラーが発生しました。", "stamprally005-init-update-stamprally-035-ERROR": "データの取得に失敗しました。", "stamprally005-init-update-stamprally-036-ERROR": "データの取得に失敗しました。", "stamprally005-init-update-stamprally-037-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-038-ERROR": "データの登録に失敗しました。", "stamprally005-init-update-stamprally-039-ERROR": "データの登録に失敗しました。", "stamprally005-init-update-stamprally-040-ERROR": "データの取得に失敗しました。", "stamprally005-init-update-stamprally-041-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-042-ERROR": "common-wallet-chargeの実行に失敗しました。", "stamprally005-init-update-stamprally-043-ERROR": "データの登録に失敗しました。", "stamprally005-init-update-stamprally-044-ERROR": "データの取得に失敗しました。", "stamprally005-init-update-stamprally-045-ERROR": "データの取得に失敗しました。", "stamprally005-init-update-stamprally-046-ERROR": "購入上限を超えています。", "stamprally005-init-update-stamprally-047-ERROR": "販売期間外です。", "stamprally005-init-update-stamprally-048-ERROR": "在庫がありません。", "stamprally005-init-update-stamprally-049-ERROR": "シリアルコードを取得できませんでした。", "stamprally005-init-update-stamprally-050-ERROR": "データの更新に失敗しました。", "stamprally005-init-update-stamprally-051-ERROR": "データの取得に失敗しました。", "stamprally005-init-update-stamprally-052-ERROR": "common-wallet-paymentの実行に失敗しました。", "stamprally005-init-update-stamprally-053-ERROR": "データの登録に失敗しました。", "stamprally001-init-get-stamprally-list-002-ERROR": "システムエラーが発生しました。", "stamprally001-init-get-stamprally-list-003-ERROR": "システムエラーが発生しました。", "stamprally001-init-get-stamprally-list-004-ERROR": "システムエラーが発生しました。", "stamprally001-init-get-stamprally-list-005-ERROR": "システムエラーが発生しました。", "stamprally001-init-get-stamprally-list-006-ERROR": "システムエラーが発生しました。", "stamprally001-init-get-stamprally-list-007-ERROR": "システムエラーが発生しました。", "stamprally002-init-get-stamprally-detail-002-ERROR": "システムエラーが発生しました。", "stamprally002-init-get-stamprally-detail-003-ERROR": "システムエラーが発生しました。", "stamprally002-init-get-stamprally-detail-004-ERROR": "システムエラーが発生しました。", "stamprally002-init-get-stamprally-detail-005-ERROR": "システムエラーが発生しました。", "stamprally002-init-get-stamprally-detail-006-ERROR": "システムエラーが発生しました。", "stamprally002-init-get-stamprally-detail-007-ERROR": "システムエラーが発生しました。", "stamprally002-init-get-stamprally-detail-008-ERROR": "システムエラーが発生しました。", "stamprally002-init-get-stamprally-detail-009-ERROR": "システムエラーが発生しました。", "stamprally002-init-get-stamprally-detail-010-ERROR": "システムエラーが発生しました。", "stamprally002-init-get-stamprally-detail-011-ERROR": "システムエラーが発生しました。", "stamprally002-init-get-stamprally-detail-012-ERROR": "システムエラーが発生しました。", "stamprally003-init-get-stamprally-item-002-ERROR": " システムエラーが発生しました。", "stamprally003-init-get-stamprally-item-003-ERROR": " システムエラーが発生しました。", "stamprally003-init-get-stamprally-item-004-ERROR": " システムエラーが発生しました。", "nft": {"nft001": {"nft_categories_list": "NFTカテゴリ－一覧", "no_nf_ts_available_for_purchase": "購入可能なNFTはありません"}, "nft002": {"list_of_nf_ts": "NFT一覧", "no_nf_ts_available_for_purchase": "購入可能なNFTはありません", "show_more": "もっと見る", "not_acquired": "未取得", "newest": "新着順", "sorted_by_popularity": "人気順", "order_of_sales": "売上順"}, "nft003": {"this_nft_is_now_sold_out": "このNFTは完売しました", "this_nft_is_not_for_sale": "このNFTは非売品です", "this_nft_is_no_longer_available_for_sale": "このNFTは販売終了しました", "rest": "残り", "point": "点", "limited": "限定", "benefit_ticket": "特典クーポン", "card_id": "カードID", "card_information": "カード情報", "sales_start": "販売開始", "end_of_sale": "販売終了", "sale_ends_when_stock_runs_out": "在庫がなくなり次第販売終了", "purchase_with_coins": "コインで購入", "purchase_with_credit_card": "クレジットカードで購入", "back": "戻る", "nf_ts_with_the_same_design_are": "同じ図柄のNFTは", "up_to_times": "回まで", "available_for_purchase": "購入可能です", "local_location_information_required_for_purchase": "購入には現地の位置情報が必要です", "share_this_nft": "このNFTをシェア", "get_card": "カードを入手"}, "nft005": {"point_your_camera_at_the_qr_code": "QRコード読み取り", "point_the_power_mela_at_the_qr_code": "力メラをQRコードに向けてください", "i_can_t_read_the_qr_code": "QRコードが読み取れない"}, "nft006": {"screen_title": "QRコード番号入力", "qr_code_number_label": "QRコード番号", "qr_code_number_placeholder": "# QRコード番号を入力してください", "cautionary_note": "※QRコードは各店舗でご確認ください。"}, "nft007": {"screen_title": "位置情報確認", "title_get_position_done": "位置情報を取得しました", "title_get_position_fail": "位置情報が取得できませんでした", "subtitle_get_position_fail": "以下の方法をお試しください", "ios_header": "iOSをお使いの場合", "ios_description1": "(1) ブラウザ(Safari)から設定する場合", "ios_description2": "(2) 「設定」から設定する場合", "ios_description3": "(3) 上記の方法で解決されない場合", "ios_sub_description3": "こちらの方法を合わせてご確認ください。", "ios_description_support": "https://support.apple.com/ja-jp/HT207092", "android_header": "Androidをお使いの場合", "android_description1": "(1)「設定」から設定する場合", "android_description2": "(2) 上記の方法で解決されない場合", "android_sub_description2": "こちらの方法を合わせてご確認ください。", "android_description_support": "https://support.google.com/accounts/answer/3467281?hl=ja", "next_button": "次へ", "get_posotion_button": "もう一度位置情報を取得", "cancel_purchase_button": "購入をキャンセル", "message_get_position_error1": "位置情報の利用が許可されていません。", "message_get_position_error2": "位置情報の取得に失敗しました。"}, "nft008": {"screen_title": "シリアル番号選択", "can_buy": "を購入できます。", "total_amount_label": "合計", "coin_balance_label": "コイン残高", "charge_button": "チャージ", "payment_amount_label": "今回のお支払い", "payment_balance_label": "お支払い後の残高", "info_text2": "シリアルナンバーを選択してください", "cancel_button": "キャンセル", "purchase_button": "購入", "placeholder_serial_code": "シリアルナンバーを選択", "cautionary_note": {"text1": "※ 同じ図柄のNFTは", "text2": "回まで", "text3": "購入可能です"}, "payment_store_label": "使用する対象"}, "nft009": {"screen_title": "支払い確認", "info_text": "お支払い内容をご確認ください", "total_amount_label": "合計", "coin_balance_label": "コイン残高", "payment_amount_label": "今回のお支払い", "payment_balance_label": "お支払い後の残高", "cancel_button": "キャンセル", "purchase_button": "購入", "serial_number_label": "シリアル番号", "sale_end_info": "在庫がなくなり次第販売終了", "unknown": "エラーが発生しました。", "purchase_failed": "取引が失敗しました。", "payment_store_label": "使用する対象"}, "nft010": {"info_text1": "支払いを受け付けました。", "sub_info_text1": "NFTを発行しています...", "info_text2": "発行が完了したらメールでお知らせします。", "sub_info_text2": "※ 発行には数分かかります", "nft_list_button": "NFT一覧 ", "nft_home_button": "NFTホーム ", "info_text_ticket_type": {"1": "チケットが使用できるようになりました", "2": "チケットが購入可能になりました"}, "coupon_label": "特典クーポン", "ticket_check_button": "チケットを確認"}, "nft011": {"title_header": "支払い", "remaining_payment_amount": "支払い金額のこり", "unit": "円", "medal_info": "使える対象"}, "nft_collection001": {"date_of_issue": "発行日", "category": "カテゴリ", "serial_number": "シリアルナンバー", "collection": "コレクション", "express": "表示", "see_more": "もっと見る"}, "nft_collection002": {"copyright_source": "版権元", "maker": "制作者", "issuer": "発行者", "token_id": "トークンID", "token_standard": "トークン規格", "blockchain": "ブロックチェーン", "content_description": "コンテンツ説明", "content_image_url": "コンテンツ画像URL", "date_of_issue": "発行日", "card_id": "カードID", "serial_number": "シリアルナンバー", "close": "閉じる", "see_more": "もっと見る", "share_this_nft": "このNFTをシェア", "contract_address": "コントラクトアドレス", "collection_details": "コレクション詳細"}, "step": [{"text": "NFTアドレス認証"}, {"text": "位置情報認証"}, {"text": "購入可能"}]}, "item": {"item001": {"no_time_specified": "時間指定なし", "no_event_date": "開催日なし　時間指定なし", "free": "無料", "unit": " 円", "it_is_in": "にある ", "item_list": "アイテム一覧", "there_are_no_items": "アイテムがありません", "no_date_specified": "開催日なし "}, "item002": {"sort": "並び替え", "keyword_search": "キーワード検索", "keyword": "キーワード", "new_arrival_order": "新着順", "oldest": "古い順", "ticket": "チケット", "not_utilized": "未利用", "used": "利用済", "all": "すべて", "narrow_down": "絞り込み", "kinds": "種類", "usage_situation": "利用状況", "location": "場所（都道府県）", "reset": "リセット"}}, "community": {"community001": {"header": "チャット", "last_read": "ーーーーーーーーー最後の既読ーーーーーーーーー", "message_deleted": "メッセージが削除されました", "upload_image": "画像のアップロード", "image": "画像", "max_upload_size": "※アップロード上限サイズ：4MB", "change_image": "※画像をクリックすると画像を変更できます。", "select_image": "＋画像を選択", "post_a_chat": "チャットを投稿", "enter_title": "タイトルを記入", "fill_in_text": "新しく投稿してみましょう！", "copied": "コピーされました", "keep": "保存", "edited": "編集済み", "sure_to_delete_thread": "投稿内容を\n削除してもよろしいですか?", "cancel": "キャンセル", "delete": "削除", "type_reply": "返信を入力", "show_reply": "{0}件の返信を表示", "show_more_reply": "さらに返信を表示", "type_message": "メッセージを入力してください", "new_label": "新しい投稿", "update_label": "メッセージを編集"}, "community002": {"list_of_categories_and_channels": "カテゴリ・チャネル一覧", "create_a_new_category": "カテゴリ新規作成", "mention": "メンション", "create": "新規作成", "edit": "編集", "editing": "編集中", "finish_editing": "編集終了", "reacted_to_you": "{0}さんがあなたにリアクションしました", "mentioned_you": "{0}さんがあなたにメンションしました"}, "community003": {"header": "新規登録", "category_name_label": "カテゴリ名", "channel_name_label": "チャネル名", "confirm_button": "作成", "create_button": "作成", "back_to_list_button": "一覧に戻る", "name_label_limit": "30文字まで", "category_name_placeholder": "カテゴリ名を記載してください。", "channel_name_placeholder": "チャネル名を記載してください。", "done": "完了しました。", "error_permission": "権限がありません。", "completed_msg": "作成が完了しました"}}, "top": {"top001": {"click_here_for_first_time": "初めてご利用される方はこちら", "special_features": "特集", "search_purpose": "目的から探す", "see_more": "もっと見る", "event_date": "開催日：", "purpose": {"0": "食べる", "1": "買う", "2": "体験する", "3": "イベント", "4": "スタンプラリー", "5": "ミッション", "6": "クーポン", "7": "EC", "8": "NFTマーケット", "9": "地域コミュニティ"}, "up_to": "まで"}, "top002": {"header": "DAO参加", "dao_participate_text": "以下のコミュニティに参加しますか？", "dao_name_label_text": "コミュニティ名", "over_view_label_text": "概要", "content_label_text": "活動内容", "participant_count_label_text": "参加者数", "count_unit_text": "人", "gt_unit_text": "GT", "issue_gt_count_label_text": "総発行GT", "allocation_gt_label_text": "自動割り当てGT", "nick_name_label_text": "ニックネーム", "nick_name_placeholder": "ニックネーム", "message_label_text": "コミュニティ公開する一言メッセージ", "message_placeholder": "よろしくお願いします。", "cancel_button_text": "キャンセル", "participate_button_text": "参加する"}, "top003": {"header": "プロフィール", "update_button_text": "変更", "nick_name_label": "ニックネーム", "message_label": "コミュニティに公開する一言メッセージ", "governance_token_label": "保有GT", "back_button_text": "とじる", "exit_button_text": "コミュニティを退出", "exit_label": "のコミュニティを退出しますか？", "cancel_button": "キャンセル", "exit_confirmed_button": "退出", "done_label": "コミュニティを退出しました", "exit_back_button_text": "トップへ戻る", "role_label": "役割", "role": {"role_host": "主催者", "role_administrator": "管理者", "role_participant": "参加者"}}, "top004": {"header": "プロフィール", "nick_name_label": "ニックネーム", "nick_name_placeholder": "ニックネーム", "message_label": "コミュニティに公開する一言メッセージ", "message_placeholder": "コミュニティに公開する一言メッセージ", "confirmation_button_text": "確認画面へ", "back_button_text": "もどる"}, "top005": {"header": "コミュニティ概要", "community_name": "コミュニティ名", "overview": "概要", "activity_content": "活動内容", "target": "目標", "participant_number": "参加者数", "participant_unit": "人", "total_GT": "総発行GT", "automatic_GT": "自動割り当てGT", "back_to_community_top": "コミュニティTOPへ", "GT": "GT", "blockchain_label": "ブロックチェーン利用", "and": "あり", "without": "なし"}, "top006": {"screen_title": "メンバー", "role": {"role_host": "主催者", "role_administrator": "管理者", "role_participant": "参加者"}}, "top_preview": {"header": "プロフィール", "nick_name_label": "ニックネーム", "message_label": "コミュニティに公開する一言メッセージ", "done_button_text": "完了", "cancel_button_text": "キャンセル"}}, "voting": {"voting001": {"header": "投票一覧", "narrow_down": "絞り込み", "search_result": "検索結果 {0} 件", "label": {"before_voting": "受付前", "voting_in_progress": "受付中", "end": "受付終了", "voted": "投票済", "not_voted_yet": "未投票"}, "button": {"next_5_items": "次の5件を表示", "create_voting_theme": "投票テーマ作成"}, "filter": {"header": "絞り込み", "voting_reception": "受付状況", "before_reception": "受付前", "in_progress_reception": "受付中", "end_reception": "受付終了", "voting_status": "ステータス", "not_voted": "未投票", "voted": "投票済", "voting_period": "期間", "start_date": "開始日", "end_date": "終了日", "display_order": "並び替え", "reception_start_date": "受付開始日", "reception_end_date": "受付終了日", "filter_by_this_content": "この内容で絞り込み", "new": "新", "old": "旧"}}, "voting004": {"header": "投票内容", "header_edit": "投票内容編集", "voting_success_countCaution": "※投票成立数が不要な場合は０を記入してください \n※初期状態では作成時のパラメータが入力されています", "conversionGT_countCaution": "※初期状態では、DAO作成時のパラメータが設定されています", "voting_format_code_placeholder": "投票形式を選択してください", "voting_format_code": {"regular_voting": "通常投票", "single_selection": "単一選択", "multiple_selection": "複数選択"}, "voting_title_placeholder": "投票名をご記入ください", "voting_format_label": "投票形式の選択", "voting_name_label": "投票名", "voting_explanation_label": "投票説明", "voting_explanation_placeholder": "投票説明内容をご記入ください", "choices_label": "選択肢", "choice_image": "選択肢{0}画像", "number_of_votes_label": "投票成立数", "number_of_votes": "{0}票", "vote_converted_to_GT_label": "1票換算GT", "voting_period_label": "投票期間", "voting_candidate_addButton": "選択肢を追加", "voting_candidate_delete_button": "選択肢削除", "voting_candidate_placeholder": "選択肢をご記入ください", "complete_button_create": "投稿する", "complete_button_update": "更新する", "cancel_button": "キャンセルする", "close_button": "とじる", "csv_tips": {"regular_voting": "通常投票", "regular_voting_content": "賛成・反対・どちらでもないの3択から投票します。", "single_selection": "単一選択投票", "single_selection_content": "1種類の選択肢に投票します。", "multiple_selection": "複数選択投票", "multiple_selection_content": "複数の選択肢に投票します。"}, "up_to_characters": "{0}文字まで", "image_MaxFileSize": "※アップロード上限サイズ：4MB", "image_caution": "画像をクリックすると画像を変更できます。", "image": "投票画像", "select_image": "+ 画像を選択", "voting_explanation_delete_button": "画像削除", "voting_start_dateTime": "開始日時", "voting_end_dateTime": "終了日時", "voting_theme_confirm_button": "確認画面へ", "voting_theme_update_button": "更新", "voting_theme_delete_button": "削除", "voting_theme_back_button": "一覧に戻る", "done": "完了しました。", "expect_quantity_item": "選択肢は{0}つ以上作成してください", "no_more_quantity_item": "選択肢は{0}以下にしてください", "expect_quantity_item_and_empty_content": "必須項目です。削除するか記入してください。", "delete_done": "削除が完了しました。", "error_permission": "権限がありません。", "delete_content_1": "投票", "delete_content_2": "を削除してよろしいですか？"}, "voting002": {"header_detail": "投票内容", "header_result": "投票結果", "label": {"before_voting": "受付前", "voting_in_progress": "受付中", "end": "受付終了", "voted": "投票済", "not_voted_yet": "未投票"}, "voting_not_possible": "所持しているGTが不足しているため投票できません", "period": "期間", "my_voting_result": "自分の投票結果", "voting_result": "投票結果　全{0} 票", "voting_history_csv": "CSVダウンロード", "owned_gt": "所持しているGT", "owned_gt_had": "投票時に所持していたGT", "voting_date_time": "投票日時", "not_voted": "未投票", "GT_unit": "GT", "vote_unit": "票", "required_GT_1unit": "1票に必要なGT", "owned_vote_count": "自身の投票数", "vote_notice": "ご自身の投票数分、投票に反映されます。", "current_number_vote": "現在の投票数", "number_of_vote": "投票数", "number_success_vote": "投票成立数", "voting_date": "期間", "back_button": "一覧に戻る", "vote": "投票する", "vote_again": "再投票する", "completed": "完了しました。", "voting_passed": "投票成立", "voting_failed": "投票不成立", "blockchain_registration_info": "ブロックチェーン登録情報", "contract_address": "コントラクトアドレス", "transaction_hash": "トランザクションハッシュ", "voting_id": "投票ID", "blockchain": "ブロックチェーン", "voting_establish_datetime": "投票情報登録日時", "csv_tutorial_header": "各投票結果における投票履歴を以下のフォーマットのCSV形式でダウンロードできます。\n投票者名，投票先，投票数", "example": "例", "tips_title": "Tips", "tips_content1": "ニックネーム", "tips_content2": "選択肢A", "tips_content3": "100票", "copied": "コピーされました", "right_to_vote_1": "あなたの投票権利は", "right_to_vote_2": "票分です", "cannot_vote": "投票できません。管理者に連絡してください。", "community_name": "コミュニティ名", "voting_name": "投票名", "publication_date": "発行日", "poll_id": "投票ID", "blockchain_recording_address": "ブロックチェーン記録先アドレス", "preparing_for_blockchain": "ブロックチェーン発行準備中…"}, "voting003": {"completed": "投票が完了しました", "back_button": "投票にもどる"}, "voting005": {"create_done": "作成が完了しました", "delete_done": "削除が完了しました", "update_done": "更新が完了しました", "back_voting_list": "一覧に戻る", "back_voting_detail": "投票に戻る"}}, "dao_community": {"community_004": {"screen_title": "カテゴリ・チャネル一覧", "category_label": "カテゴリ名", "category_placeholder": "カテゴリ名を記載してください", "up_to_characters": "{0}文字まで", "btn_edit": "更新", "btn_delete": "削除", "btn_back": "一覧に戻る", "done": "完了しました。", "type": "カテゴリ", "information_delete": "\nを削除してよろしいですか？", "warning_message": "※カテゴリを削除するとチャネルも削除されます", "cancel_btn": "キャンセル", "delete_confirmed_button": "削除", "error_permission": "権限がありません。", "delete_label": "削除が完了しました", "done_label2": "更新が完了しました"}, "community_005": {"screen_title": "カテゴリ・チャネル一覧", "category_label": "カテゴリ名", "category_placeholder": "カテゴリ名を記載してください", "channel_label": "チャネル名", "channel_placeholder": "チャネル名を記載してください", "up_to_characters": "{0}文字まで", "btn_create": "作成", "btn_back": "一覧に戻る", "done": "完了しました。", "error_permission": "権限がありません。", "done_label": "作成が完了しました"}, "community_006": {"screen_title": "カテゴリ・チャネル一覧", "category_label": "カテゴリ名", "channel_label": "チャネル名", "channel_placeholder": "チャネル名を記載してください", "up_to_characters": "{0}文字まで", "btn_edit": "更新", "btn_delete": "削除", "btn_back": "一覧に戻る", "done": "完了しました。", "type": "チャネル", "information_delete": "\nを削除してよろしいですか？", "warning_message": "カテゴリを削除すると以下のチャネルも削除されます", "cancel_btn": "キャンセル", "delete_confirmed_button": "削除", "channel_list_length_message": "チャネルは1つ以上必要です。", "error_permission": "権限がありません。", "delete_label": "削除が完了しました", "done_label2": "更新が完了しました"}}, "mobile_order": {"item001": {"screen_title": "商品一覧", "cart_label": "カート", "history_label": "履歴・お会計", "search_placeholder": "何をお探しですか？", "no_result": "商品がありません", "next_button_text": "次の{0}件を表示", "tax_label": "（税込）"}, "item002": {"screen_title": "商品詳細", "tax_label": "（税込）", "cart_label": "カート", "history_label": "履歴・お会計", "quantity": "数量", "sold_out_label": "売り切れ", "explanation_label": "商品説明", "product_addition_error": "注文数が注文可能数を超えているため\nカートに追加できませんでした。", "product_limit_error": "商品数が上限を超えているため\nカートに追加できませんでした。", "add_product_success": "商品をカートに追加しました。", "add_cart_button": "カートに追加する", "top_button": "商品一覧に戻る", "item_status_message_1": "この商品は削除されています", "item_status_message_2": "この商品は現在注文できません", "item_status_message_3": "この商品は販売開始前です", "item_status_message_4": "この商品は販売が終了しています"}, "confirm001": {"header": "注文完了", "text": "注文を受け付けました", "item_button": "注文を続ける", "history_button": "履歴を見る・お会計に進む"}, "cart001": {"title": "カート", "no_product": "カートに商品が入っていません", "quantity_label": "数量", "go_to_product_details": "商品詳細へ", "delete_item": "削除する", "tax_included": "(税込)", "sub_total": "小計", "total_count": "（{0}商品）", "confirm_button": "注文を確定する", "top_button": "商品一覧に戻る", "inconsistent_datetime": "カートの内容が変更されています。\n画面を更新して下さい。", "product_update": "情報が更新されている商品があります。\n商品情報を確認して下さい。", "deleted_item_status_1": "この商品は削除されています", "deleted_item_status_2": "この商品は現在注文できません", "deleted_item_status_3": "この商品は販売開始前です", "deleted_item_status_4": "この商品は販売が終了しています", "check_product_detail": "商品詳細を確認してください", "over_stock": "数量が在庫数を超えています"}, "history001": {"title": "注文履歴", "tax_label": "（税込）", "quantity_label": "数量", "sub_total_label": "合計", "btn": "お会計に進む", "goods": "商品", "error_text": "注文が確定している商品がありません。", "top_button": "商品一覧に戻る"}}, "brand": {"brand001": {"community_list": "地域一覧", "join": "参加する", "detail": "詳細"}, "brand002": {"title": "参加しますか？", "join": "参加する"}}, "qrcode_present": {"qrcode_present001": {"title": "QRコード表示"}, "qrcode_present002": {"title": "支払い完了", "payment_completed_info": "支払いが完了しました", "btn": "ホームに戻る", "got_point": "取得しました", "of": "を"}, "qrcode_present003": {"title": "チャージ完了", "charge_completed_info": "チャージしました", "effective_date": "有効期限：", "btn": "ホームに戻る"}}, "dao_top001": {"about_community": "コミュニティ\nについて", "view_profile": "プロフィール\nを見る", "join_chat": "チャット\nに参加する", "vote_idea": "アイデアに\n投票する"}, "common_qr": {"code_read001": {"title": "QRコード読み取り", "err_message": "店舗アドレスが不正です", "mess_qr_invalid": "QRコードが不正です"}, "code_read002": {"title": "コードで入金", "label": "コードを入力", "sub_label": "コードはQRコードの下にある10~16ケタの英数字です。", "place_holder": "入力してください", "mess_qr_invalid": "コードが不正です", "mess_qr_required": "コードを入力してください", "incorrect_format": "半角数字10文字または半角英数字16文字を入力しください"}}, "my_number": {"error_title": "対応環境エラー", "error_content": "非対応環境のため本人確認できません。\nAndroid、iOSで本人確認を行ってください。", "my_number001": {"title": "データ利用同意", "description": "マイナンバーカードの券面事項入力補助の利用に関する同意確認", "message": "利用者登録に際し、マイナンバーカード記載の４情報について、券面事項入力補助を利用し、４情報（氏名・住所・生年月日・性別）を取得することに同意します。", "agree": "同意する", "not_agree": "同意しない"}, "my_number002": {"title": "マイナンバーカード読取\n（利用者証明用電子証明書）", "description": "画面下にある「読み取り開始」ボタンをクリックすることで、マイナポータルアプリを起動し、マイナンバーカードの利用者証明用電子証明書読み取りを行ってください。", "read_btn": "読み取り開始", "cancel_btn": "キャンセル", "back_btn": "戻る"}, "my_number003": {"title": "マイナンバーカード読取\n（利用者証明用電子証明書）", "description": "この画面が表示された場合は、ブラウザを閉じてください", "read_btn": "読み取り開始", "cancel_btn": "閉じる", "load_error": "読込エラー", "system_error": "システムエラー"}, "my_number004": {"title": "マイナンバーカード読み取り内容確認", "description": "マイナンバーカードの券面事項を読み取りました。\n以下の情報が正しいか、ご確認ください。\n\n正しくない場合、市役所窓口で券面事項の変更手続きを行ってからマイナンバーカード認証してください。\n※性別は非表示にしています。", "execute_btn": "上記内容で登録する", "cancel_btn": "戻る", "card_title": "■基本４情報（マイナンバーカード券面事項）", "full_name": "氏名", "address": "住所", "birthday": "生年月日"}, "my_number005": {"title": "マイナンバーカード読取\n（券面事項取得）", "description": "画面下にある「読み取り開始」ボタンをクリックすることで、マイナポータルアプリを起動し、マイナンバーカードの券面事項読み取りを行ってください。\n※マイナンバーは読み取りません。", "read_button": "読み取り開始", "close_button": "閉じる", "confirm_message": "画面を閉じて、処理を中断します。よろしいでしょうか？"}, "my_number006": {"title": "マイナンバーカード読取\n（券面事項取得）", "description": "この画面が表示された場合は、ブラウザを閉じてください。", "system_error": "システムエラー"}, "my_number008": {"screen_title": "マイナンバーカード読取内容確認", "message": "マイナンバーカードの券面事項を読み取りました。\n以下の情報が正しいか、ご確認ください。\n\n正しくない場合、市役所窓口で券面事項の変更手続きを行ってからマイナンバーカード認証してください。\n※性別は非表示にしています。", "card_title": "■基本４情報（マイナンバーカード券面事項）", "full_name": "氏名", "address": "住所", "birthday": "生年月日", "execute_button": "上記内容で登録する", "close_button": "閉じる", "confirm_message": "画面を閉じて、処理を中断します。よろしいでしょうか？"}, "my_number009": {"title": "マイナンバーカード認証結果", "description": "マイナンバーカードを認証しました。"}, "my_number011": {"header": "新規登録", "card_title": "■基本４情報（マイナンバーカード券面事項）", "full_name": "氏名", "address": "住所", "birthday": "生年月日", "mail_address_label": "メールアドレス", "mail_address_sub_label": "例 : <EMAIL>", "mail_address_placeholder": "メールアドレスを入力してください", "password_text": "パスワード", "password_label": "パスワードの設定", "password_sub_label": "半角英数字8文字以上で設定ください", "password_placeholder": "パスワードを入力してください", "confirm_password_label": "パスワード再入力", "confirm_password_placeholder": "再度パスワードを入力してください", "member_number_label": "会員ナンバー", "member_number_ext_label": "会員ナンバーをお持ちの方は、お持ちの番号をそのまま入力してください", "member_number_sub_label": "例 : TYK0123456789", "member_number_sub_placeholder": "会員ナンバーを入力してください", "password_must_have_letter_digit": "パスワードには英字、数字を含めてください。", "password_incorrect_message": "パスワードが誤っています。", "password_not_match": "パスワードとパスワード（確認）が異なっています。", "check_member_number_first_5_letters": "入力形式が正しくありません", "check_member_number_input": "4～13文字目は数字を入力してください", "check_member_number_length": "13文字で入力してください", "sign_up": "新規登録", "cellphone_no_info_label": "携帯電話番号", "cellphone_no_info_sub_label": "例：***********", "cellphone_no_info_placeholder": "携帯電話番号を入力してください", "cellphone_no_error": "携帯電話番号が正しくありません"}}, "my_number_test": {"error_title": "対応環境エラー", "error_content": "非対応環境のため本人確認できません。\nAndroid、iOSで本人確認を行ってください。", "my_number002": {"title": "マイナンバーカード読取\n（利用者証明用電子証明書）", "description": "画面下にある「読み取り開始」ボタンをクリックすることで、マイナポータルアプリを起動し、マイナンバーカードの利用者証明用電子証明書読み取りを行ってください。", "read_btn": "読み取り開始", "cancel_btn": "キャンセル", "back_btn": "戻る"}, "my_number003": {"title": "マイナンバーカード読取\n（利用者証明用電子証明書）", "description": "この画面が表示された場合は、ブラウザを閉じてください", "read_btn": "読み取り開始", "cancel_btn": "閉じる", "load_error": "読込エラー", "system_error": "システムエラー"}, "my_number004": {"title": "マイナンバーカード読み取り内容確認", "description": "マイナンバーカードの券面事項を読み取りました。\n以下の情報が正しいか、ご確認ください。\n\n正しくない場合、市役所窓口で券面事項の変更手続きを行ってからマイナンバーカード認証してください。\n※性別は非表示にしています。", "execute_btn": "上記内容で登録する", "cancel_btn": "戻る", "card_title": "■基本４情報（マイナンバーカード券面事項）", "full_name": "氏名", "address": "住所", "birthday": "生年月日"}, "my_number005": {"title": "マイナンバーカード読取\n（券面事項取得）", "description": "画面下にある「読み取り開始」ボタンをクリックすることで、マイナポータルアプリを起動し、マイナンバーカードの券面事項読み取りを行ってください。\n※マイナンバーは読み取りません。", "read_button": "読み取り開始", "close_button": "閉じる", "confirm_message": "画面を閉じて、処理を中断します。よろしいでしょうか？"}, "my_number006": {"title": "マイナンバーカード読取\n（券面事項取得）", "description": "この画面が表示された場合は、ブラウザを閉じてください。", "system_error": "システムエラー"}, "my_number008": {"screen_title": "マイナンバーカード読取内容確認", "message": "マイナンバーカードの券面事項を読み取りました。\n以下の情報が正しいか、ご確認ください。\n\n正しくない場合、市役所窓口で券面事項の変更手続きを行ってからマイナンバーカード認証してください。\n※性別は非表示にしています。", "card_title": "■基本４情報（マイナンバーカード券面事項）", "full_name": "氏名", "address": "住所", "birthday": "生年月日", "execute_button": "上記内容で登録する", "close_button": "閉じる", "confirm_message": "画面を閉じて、処理を中断します。よろしいでしょうか？", "yes": "はい", "no": "いいえ", "elasticache_information_label": "Elasticache情報", "myna_session_id_label": "セッションID", "from_screen_label": "FromScreen", "brand_ID_label": "ブランドID", "cheer_ID_label": "CheerID", "os_flag_label": "OSフラグ", "myna_mail_address_label": "メールアドレス", "myna_end_user_id_label": "エンドユーザID", "myan_validate_result_label": "検証結果", "myan_revocation_reason_code_label": "失効理由コード", "myan_revocation_reason_message_label": "失効理由メッセージ", "myan_process_result_label": "処理結果"}, "my_number009": {"title": "マイナンバーカード認証結果", "description": "マイナンバーカードを認証しました。"}, "my_number011": {"card_title": "■基本４情報（マイナンバーカード券面事項）", "full_name": "氏名", "address": "住所", "birthday": "生年月日", "mail_address_label": "メールアドレス", "mail_address_sub_label": "例 : <EMAIL>", "mail_address_placeholder": "メールアドレスを入力してください", "password_text": "パスワード", "password_label": "パスワードの設定", "password_sub_label": "半角英数字8文字以上で設定ください", "password_placeholder": "パスワードを入力してください", "confirm_password_label": "パスワード再入力", "confirm_password_placeholder": "再度パスワードを入力してください", "member_number_label": "会員ナンバー", "member_number_ext_label": "会員ナンバーをお持ちの方は、お持ちの番号をそのまま入力してください", "member_number_sub_label": "例 : TYK0123456789", "member_number_sub_placeholder": "会員ナンバーを入力してください", "password_must_have_letter_digit": "パスワードには英字、数字を含めてください。", "password_incorrect_message": "パスワードが誤っています。", "password_not_match": "パスワードとパスワード（確認）が異なっています。", "check_member_number_first_5_letters": "入力形式が正しくありません", "check_member_number_input": "4～13文字目は数字を入力してください", "check_member_number_length": "13文字で入力してください", "sign_up": "新規登録"}, "my_number000": {"screen_title": "メニュー・新規からマイナ連携", "menu_button": "メニューからマイナ連携", "mail_address_label": "メールアドレス", "mail_address_sub_label": "例 : <EMAIL>", "mail_address_placeholder": "メールアドレスを入力してください", "entry_button": "新規登録からマイナ連携", "ec_button": "ElastiCache情報表示"}}, "chargeqrcodepresent001-init-create-transfercode-002-ERROR": "リクエストパラメータが異常です。", "chargeqrcodepresent001-init-create-transfercode-003-ERROR": "データの登録に失敗しました。", "eventform002-main-send-answer-001-ERROR": "リクエストパラメータが異常です。", "eventform002-main-send-answer-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-main-send-answer-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-main-send-answer-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-main-send-answer-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-main-send-answer-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-main-send-answer-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-main-send-answer-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-main-send-answer-009-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-main-send-answer-010-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-main-send-answer-011-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform001-init-get-eventform-list-002-ERROR": "システムエラーが発生しました。", "eventform001-init-get-eventform-list-003-ERROR": "システムエラーが発生しました。", "eventform001-init-get-eventform-list-004-ERROR": "システムエラーが発生しました。", "eventform001-init-get-eventform-list-005-ERROR": "システムエラーが発生しました。", "eventform001-init-get-eventform-list-006-ERROR": "システムエラーが発生しました。", "eventform001-init-get-eventform-list-007-ERROR": "システムエラーが発生しました。", "eventform002-init-get-eventform-detail-001-ERROR": " リクエストパラメータが異常です。", "eventform002-init-get-eventform-detail-002-ERROR": " システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-init-get-eventform-detail-003-ERROR": " 申込み受付期間外です。", "eventform002-init-get-eventform-detail-004-ERROR": " 申込み受付を終了しました。", "eventform002-init-get-eventform-detail-005-ERROR": " システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-init-get-eventform-detail-006-ERROR": " システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-init-get-eventform-detail-007-ERROR": " データが見つかりませんでした。しばらく時間をおいてから再度お試しください。", "eventform002-init-get-eventform-detail-008-ERROR": " システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "eventform002-init-get-eventform-detail-009-ERROR": " 既に回答を受付済です。", "chargeqrcoderead001-main-execute-charge-001-INFO": "", "chargeqrcoderead001-main-execute-charge-002-ERROR": "リクエストパラメータが異常です。", "chargeqrcoderead001-main-execute-charge-003-ERROR": "データの取得に失敗しました。", "chargeqrcoderead001-main-execute-charge-004-ERROR": "データの取得に失敗しました。", "chargeqrcoderead001-main-execute-charge-005-ERROR": "システムエラーが発生しました。", "chargeqrcoderead001-main-execute-charge-006-ERROR": "ポイントのチャージに失敗しました。", "chargeqrcoderead001-main-execute-charge-007-ERROR": "データの取得に失敗しました。", "chargeqrcoderead001-main-execute-charge-008-ERROR": "データの更新に失敗しました。", "chargeqrcoderead001-main-execute-charge-009-ERROR": "データの登録に失敗しました。", "chargeqrcoderead001-main-execute-charge-010-ERROR": "データの更新に失敗しました。", "chargeqrcoderead001-main-execute-charge-011-ERROR": "システムエラーが発生しました。", "chargeqrcoderead001-main-execute-charge-012-ERROR": "存在しないコードです。", "chargeqrcoderead001-main-execute-charge-013-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeqrcoderead001-main-execute-charge-014-ERROR": "存在しないコードです。", "chargeqrcoderead001-main-execute-charge-015-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeqrcoderead001-main-execute-charge-016-ERROR": "配布期間外もしくは配布上限回数超過です。", "chargeqrcoderead001-main-execute-charge-017-ERROR": "配布上限額超過です。", "chargeqrcoderead001-main-execute-charge-018-ERROR": "本日配布分は取得済みです。", "chargeqrcoderead001-main-execute-charge-019-ERROR": "取得済みです。再度取得可能になるまでしばらくお待ち下さい", "chargeqrcoderead002-main-execute-charge-001-INFO": "", "chargeqrcoderead002-main-execute-charge-002-ERROR": "リクエストパラメータが異常です。", "chargeqrcoderead002-main-execute-charge-003-ERROR": "データの取得に失敗しました。", "chargeqrcoderead002-main-execute-charge-004-ERROR": "データの取得に失敗しました。", "chargeqrcoderead002-main-execute-charge-005-ERROR": "システムエラーが発生しました。", "chargeqrcoderead002-main-execute-charge-006-ERROR": "ポイントのチャージに失敗しました。", "chargeqrcoderead002-main-execute-charge-007-ERROR": "データの取得に失敗しました。", "chargeqrcoderead002-main-execute-charge-008-ERROR": "データの更新に失敗しました。", "chargeqrcoderead002-main-execute-charge-009-ERROR": "データの登録に失敗しました。", "chargeqrcoderead002-main-execute-charge-010-ERROR": "データの更新に失敗しました。", "chargeqrcoderead002-main-execute-charge-011-ERROR": "システムエラーが発生しました。", "chargeqrcoderead002-main-execute-charge-012-ERROR": "本日配布分は取得済みです。", "chargeqrcoderead002-main-execute-charge-013-ERROR": "取得済みです。再度取得可能になるまでしばらくお待ち下さい", "chargeqrcoderead002-main-execute-charge-014-ERROR": "配布期間外もしくは配布上限回数超過です。", "nft001-init-get-nft-category-list-001-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft001-init-get-nft-category-list-002-ERROR": "", "nft002-main-send-nft-user-like-001-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft002-main-send-nft-user-like-002-ERROR": "", "nft002-main-send-nft-user-like-003-ERROR": "", "nft002-main-send-nft-user-like-004-ERROR": "", "nft002-main-send-nft-user-like-005-ERROR": "", "nft002-main-send-nft-user-like-006-ERROR": "", "nft002-init-get-nft-purchase-list-001-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft002-init-get-nft-purchase-list-002-ERROR": "", "nft002-init-get-nft-purchase-list-003-ERROR": "", "nft002-init-get-nft-purchase-list-004-ERROR": "", "nft002-init-get-nft-purchase-list-005-ERROR": "", "nft002-init-get-nft-category-detail-001-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft002-init-get-nft-category-detail-002-ERROR": "", "nft002-init-get-nft-category-detail-003-ERROR": "", "nft003-init-get-nft-detail-001-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft003-init-get-nft-detail-002-ERROR": "", "nft003-init-get-nft-detail-003-ERROR": "", "nft003-init-get-nft-detail-004-ERROR": "", "nft003-init-get-nft-detail-005-ERROR": "", "nft003-init-get-nft-detail-006-ERROR": "", "nft003-init-get-nft-detail-007-ERROR": "", "nft003-init-get-nft-detail-008-ERROR": "", "nft003-main-send-nft-user-like-001-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft003-main-send-nft-user-like-002-ERROR": "", "nft003-main-send-nft-user-like-003-ERROR": "", "nft003-main-send-nft-user-like-004-ERROR": "", "nft003-main-send-nft-user-like-005-ERROR": "", "nft003-main-send-nft-user-like-006-ERROR": "", "nft005-init-check-nft-qr-001-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft005-init-check-nft-qr-002-ERROR": "-", "nft005-init-check-nft-qr-003-ERROR": "-", "nft005-init-check-nft-qr-004-ERROR": "QRコード番号が不正です。", "nft005-init-check-nft-qr-005-ERROR": "-", "nft005-init-check-nft-qr-006-ERROR": "販売期間外です。", "nft008-init-get-nft-001-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft008-init-get-nft-002-ERROR": "Parameter Exception.", "nft008-init-get-nft-003-ERROR": "Get NFTContentList Exception.", "nft008-init-get-nft-004-ERROR": "該当するNFTコンテンツ情報が見つかりませんでした。", "nft008-init-get-nft-005-ERROR": "Get StoreList Exception.", "nft008-init-get-nft-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "nft008-init-get-coin-001-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "nft008-init-get-coin-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "nft008-init-get-coin-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "nft008-init-get-coin-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "nft008-init-get-coin-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "nft008-init-get-coin-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "nft008-init-get-coin-007-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "nft008-main-check-nft-serial-001-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft008-main-check-nft-serial-002-ERROR": "Parameter Exception.", "nft008-main-check-nft-serial-003-ERROR": "Get NFTContentList Exception.", "nft008-main-check-nft-serial-004-ERROR": "Get NFTContentList Exception.", "nft008-main-check-nft-serial-005-ERROR": "購入上限に達したため、購入できません", "nft008-main-check-nft-serial-006-ERROR": "販売期間外です", "nft008-main-check-nft-serial-007-ERROR": "こちらの商品は完売いたしました", "nft008-main-check-nft-serial-008-ERROR": "指定のシリアルナンバーはすでに購入されました", "nft008-main-check-nft-serial-009-ERROR": "指定のシリアルナンバーは現在購入することができません。", "nft008-main-check-nft-serial-010-ERROR": "Set NFTContentList Exception.", "nft006-init-check-nft-qr-001-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft006-init-check-nft-qr-002-ERROR": "Parameter Exception.", "nft006-init-check-nft-qr-003-ERROR": "Get StoreList Exception.", "nft006-init-check-nft-qr-004-ERROR": "QRコード番号が不正です。", "nft006-init-check-nft-qr-005-ERROR": "Get NFTContentList Exception.", "nft006-init-check-nft-qr-006-ERROR": "販売期間外です。", "nftcollection001-init-get-nft-user-list-001-ERROR": "リクエストパラメータが異常です。", "nftcollection001-init-get-nft-user-list-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nftcollection001-init-get-nft-user-list-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nftcollection001-init-get-nft-user-list-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nftcollection001-init-get-nft-user-list-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nftcollection001-init-get-nft-user-list-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nftcollection002-init-get-nft-user-001-ERROR": "リクエストパラメータが異常です。", "nftcollection002-init-get-nft-user-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nftcollection002-init-get-nft-user-003-ERROR": "このシリアルナンバーのNFTを所持していないか見つかりません", "nftcollection002-init-get-nft-user-004-ERROR": "このシリアルナンバーのNFTを所持していないか見つかりません", "nftcollection002-init-get-nft-user-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nftcollection002-init-get-nft-user-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nftcollection002-init-get-nft-user-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nftcollection002-init-get-nft-user-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "mission001-init-get-mission-list-002-ERROR": "システムエラーが発生しました。", "mission001-init-get-mission-list-003-ERROR": "システムエラーが発生しました。", "mission001-init-get-mission-list-004-ERROR": "システムエラーが発生しました。", "mission001-init-get-mission-list-005-ERROR": "システムエラーが発生しました。", "mission001-init-get-mission-list-006-ERROR": "システムエラーが発生しました。", "mission001-init-get-mission-list-007-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-002-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-003-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-004-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-005-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-006-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-007-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-008-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-009-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-010-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-011-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-012-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-013-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-014-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-015-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-016-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-017-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-018-ERROR": "システムエラーが発生しました。", "mission002-init-get-mission-detail-019-ERROR": "システムエラーが発生しました。", "mission003-init-get-mission-detail-002-ERROR": "システムエラーが発生しました。", "mission003-init-get-mission-detail-003-ERROR": "システムエラーが発生しました。", "mission003-init-get-mission-detail-004-ERROR": "システムエラーが発生しました。", "mission003-init-get-mission-detail-005-ERROR": "システムエラーが発生しました。", "mission003-init-get-mission-detail-006-ERROR": "システムエラーが発生しました。", "mission003-init-get-mission-detail-007-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "mission003-main-update-mission-003-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-004-ERROR": "QRコードが不正です。", "mission003-main-update-mission-005-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-006-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-007-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-008-ERROR": "ミッション対象範囲外です。", "mission003-main-update-mission-009-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-010-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-011-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-012-ERROR": "コードが不正です。", "mission003-main-update-mission-013-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-014-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-015-ERROR": "期間外のミッションです。", "mission003-main-update-mission-016-ERROR": "非公開のミッションです。", "mission003-main-update-mission-017-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-018-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-019-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-020-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-021-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-022-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-023-ERROR": "コードが不正です。", "mission003-main-update-mission-024-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-025-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-026-ERROR": "期間外のミッションです。", "mission003-main-update-mission-027-ERROR": "非公開のミッションです。", "mission003-main-update-mission-028-ERROR": "システムエラーが発生しました。", "mission003-main-update-mission-029-ERROR": "システムエラーが発生しました。", "商品一覧画面_商品一覧取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "商品一覧画面_商品一覧取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "商品一覧画面_商品一覧取得-004-ERROR": "EcItem Exception.", "共通機能_買い物かご情報存在確認-002-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "共通機能_買い物かご情報存在確認-003-ERROR": "EcCart Exception.", "共通機能_URL取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "共通機能_URL取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "共通機能_URL取得-004-ERROR": "Bucket Name Error.", "nft009-main-execute-payment-001-ERROR": "リクエストパラメータが異常です。", "nft009-main-execute-payment-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft009-main-execute-payment-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft009-main-execute-payment-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft009-main-execute-payment-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft009-main-execute-payment-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft009-main-execute-payment-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft009-main-execute-payment-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft007-init-get-nft-001-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "nft007-init-get-nft-002-ERROR": "Parameter Exception.", "nft007-init-get-nft-003-ERROR": "Get NFTContentList Exception.", "nft007-init-get-nft-004-ERROR": "該当するNFTコンテンツ情報が見つかりませんでした。", "nft007-init-get-nft-005-ERROR": "Get StoreList Exception.", "nft007-init-get-nft-006-ERROR": "現在の位置情報では購入できません。", "login001-main-get-line-token-002-ERROR": "リクエストパラメータが異常です。", "login001-main-get-line-token-003-ERROR": "データの取得に失敗しました。", "login001-main-get-line-token-004-INFO": "ユーザー情報が存在しませんでした。", "login001-main-get-line-token-005-ERROR": "データの取得に失敗しました。", "login001-main-get-line-token-006-INFO": "認証に失敗しました。", "login001-main-get-line-token-007-ERROR": "データの更新に失敗しました。", "共通機能_買い物かご情報更新-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "共通機能_買い物かご情報更新-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "共通機能_買い物かご情報更新-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "共通機能_買い物かご情報更新-005-ERROR": "EcItem Exception.", "共通機能_買い物かご情報更新-006-ERROR": "EcCart Exception.", "共通機能_買い物かご情報更新-007-ERROR": "EcCartDetail Exception.", "共通機能_決済期限切れ情報更新-001-INFO": "", "共通機能_決済期限切れ情報更新-002-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "共通機能_決済期限切れ情報更新-003-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "共通機能_決済期限切れ情報更新-004-ERROR": "", "共通機能_決済期限切れ情報更新-005-ERROR": "", "共通機能_決済期限切れ情報更新-006-ERROR": "", "買い物かご画面_買い物かご情報取得-001-INFO": "", "買い物かご画面_買い物かご情報取得-002-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "買い物かご画面_買い物かご情報取得-003-ERROR": "", "買い物かご画面_買い物かご情報取得-004-ERROR": "", "買い物かご画面_買い物かご情報取得-005-ERROR": "", "買い物かご画面_買い物かご情報取得-006-ERROR": "", "買い物かご画面_商品削除-001-INFO": "", "買い物かご画面_商品削除-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "買い物かご画面_商品削除-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "買い物かご画面_商品削除-004-ERROR": "情報の削除に失敗しました。再度実行して下さい。", "買い物かご画面_商品削除-005-ERROR": "", "買い物かご画面_商品削除-006-ERROR": "", "支払い画面_支払い前確認-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "支払い画面_支払い前確認-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "支払い画面_更新日時取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "支払い画面_更新日時取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "購入手続き画面_購入手続き情報更新-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "購入手続き画面_購入手続き情報更新-003-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "購入手続き画面_購入手続き情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "購入手続き画面_購入手続き情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "購入履歴画面_購入履歴一覧取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "購入履歴画面_購入履歴一覧取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "購入履歴画面_購入履歴一覧取得-004-ERROR": "EcSalesDetail Exception.", "商品詳細画面_商品情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "商品詳細画面_商品情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "common-wallet-get-payment-002-ERROR": "システムエラーが発生しました。", "common-wallet-get-payment-003-ERROR": "システムエラーが発生しました。", "common-wallet-get-payment-004-ERROR": "システムエラーが発生しました。", "common-wallet-get-payment-005-ERROR": "システムエラーが発生しました。", "common-wallet-get-payment-006-ERROR": "システムエラーが発生しました。", "common-wallet-get-payment-007-ERROR": "システムエラーが発生しました。", "投票テーマ一覧画面_投票テーマ一覧取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "投票テーマ一覧画面_投票テーマ一覧取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "投票テーマ一覧画面_投票テーマ一覧取得-004-ERROR": "DaoVotingTheme Exception.", "投票テーマ一覧画面_投票テーマ一覧取得-005-ERROR": "DaoVotingCheer Exception.", "投票テーマ一覧画面_投票テーマ一覧取得-006-ERROR": "DaoID Varidation Error.", "共通機能_機能権限取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "共通機能_機能権限取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "共通機能_機能権限取得-004-ERROR": "DaoParticipation Exception.", "共通機能_機能権限取得-005-ERROR": "DaoFunctionAuthority Exception.", "共通機能_機能権限取得-006-ERROR": "DaoID Varidation Error.", "投票テーマ管理画面_投票テーマ初期情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザを更新して下さい。", "投票テーマ管理画面_投票テーマ初期情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "投票テーマ管理画面_投票テーマ初期情報取得-004-ERROR": "DaoVotingFormat Exception.", "投票テーマ管理画面_投票テーマ初期情報取得-005-ERROR": "DaoID Varidation Error.", "投票テーマ管理画面_投票テーマ初期情報取得-006-ERROR": "DaoManagement Exception.", "投票テーマ管理画面_投票テーマ情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "投票テーマ管理画面_投票テーマ情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "投票テーマ管理画面_投票テーマ情報取得-004-ERROR": "DaoVotingTheme Exception.", "投票テーマ管理画面_投票テーマ情報取得-005-ERROR": "DaoVotingCandidate Exception.", "投票テーマ管理画面_投票テーマ情報取得-006-ERROR": "DaoID Varidation Error.", "投票テーマ管理画面_投票テーマ作成-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "投票テーマ管理画面_投票テーマ作成-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "投票テーマ管理画面_投票テーマ作成-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "投票テーマ管理画面_投票テーマ作成-005-ERROR": "権限がありません。", "投票テーマ管理画面_投票テーマ作成-006-ERROR": "DaoVotingTheme Exception.", "投票テーマ管理画面_投票テーマ作成-007-ERROR": "DaoVotingCandidate Exception.", "投票テーマ管理画面_投票テーマ作成-008-ERROR": "BrandSEQList Exception.", "投票テーマ管理画面_投票テーマ作成-009-ERROR": "FunctionAuthority Exception.", "投票テーマ管理画面_投票テーマ作成-010-ERROR": "DaoID Varidation Error.", "投票テーマ管理画面_投票テーマ更新-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "投票テーマ管理画面_投票テーマ更新-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "投票テーマ管理画面_投票テーマ更新-004-ERROR": "情報の削除に失敗しました。再度実行して下さい。", "投票テーマ管理画面_投票テーマ更新-005-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "投票テーマ管理画面_投票テーマ更新-006-ERROR": "権限がありません。", "投票テーマ管理画面_投票テーマ更新-007-ERROR": "DaoVotingTheme Exception.", "投票テーマ管理画面_投票テーマ更新-008-ERROR": "DaoVotingCandidate Exception.", "投票テーマ管理画面_投票テーマ更新-009-ERROR": "FunctionAuthority Exception.", "投票テーマ管理画面_投票テーマ更新-010-ERROR": "DaoID Varidation Error.", "投票テーマ管理画面_投票テーマ削除-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "投票テーマ管理画面_投票テーマ削除-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "投票テーマ管理画面_投票テーマ削除-004-ERROR": "情報の削除に失敗しました。再度実行して下さい。", "投票テーマ管理画面_投票テーマ削除-005-ERROR": "権限がありません。", "投票画面_投票情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "投票画面_投票情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "投票画面_投票情報取得-004-ERROR": "DaoVotingTheme Exception.", "投票画面_投票情報取得-005-ERROR": "DaoVotingCheer Exception.", "投票画面_投票情報取得-006-ERROR": "DaoVotingCandidate Exception.", "投票画面_投票情報取得-007-ERROR": "DaoVotingFormat Exception.", "投票画面_投票情報取得-008-ERROR": "DaoID Varidation Error.", "投票画面_投票情報取得-009-ERROR": "DaoVotingCheerDestination Exception.", "投票画面_投票情報取得-010-ERROR": "CheerList Exception.", "投票画面_投票情報取得-011-ERROR": "BlockchainMedalConfig Exception.", "投票画面_投票権取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "投票画面_投票権取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "投票画面_投票権取得-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "投票画面_投票権取得-005-ERROR": "DaoParticipant Exception.", "投票画面_投票権取得-006-ERROR": "DaoID Varidation Error.", "投票画面_投票権取得-007-ERROR": "DaoVotingTheme Exception.", "投票画面_投票権取得-008-ERROR": "API Execution Exception.", "投票画面_投票権取得-009-ERROR": "CheerList Exception.", "投票画面_投票権取得-012-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "投票画面_投票状況情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "投票画面_投票状況情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "投票画面_投票状況情報取得-004-ERROR": "DaoVotingCandidate Exception.", "投票画面_投票状況情報取得-005-ERROR": "DaoVotingTheme Exception.", "投票画面_投票状況情報取得-006-ERROR": "DaoID Varidation Error.", "投票画面_投票者情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "投票画面_投票者情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "投票画面_投票者情報取得-004-ERROR": "DaoVotingCheerHistory Exception.", "投票画面_投票者情報取得-005-ERROR": "DaoVotingCheerDestination Exception.", "投票画面_投票者情報取得-006-ERROR": "DaoVotingTheme Exception.", "投票画面_投票者情報取得-007-ERROR": "DaoID Varidation Error.", "投票画面_投票者情報取得-008-ERROR": "CheerList Exception.", "投票画面_投票-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "投票画面_投票-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "投票画面_投票-004-ERROR": "情報の削除に失敗しました。再度実行して下さい。", "投票画面_投票-005-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "投票画面_投票-006-ERROR": "DaoVotingTheme Exception.", "投票画面_投票-007-ERROR": "DaoVotingCandidate Exception.", "投票画面_投票-008-ERROR": "DaoVotingCheer Exception.", "投票画面_投票-009-ERROR": "DaoParticipant Exception.", "投票画面_投票-010-ERROR": "CheerList Exception.", "投票画面_投票-011-ERROR": "DaoVotingCheerDestination Exception.", "投票画面_投票-012-ERROR": "BrandSEQList Exception.", "投票画面_投票-013-ERROR": "DaoManagement Exception.", "投票画面_投票-014-ERROR": "DaoVotingCheerHistory Exception.", "投票画面_投票-015-ERROR": "DaoID Varidation Error.", "投票画面_投票-016-ERROR": "API Execution Exception.", "投票画面_投票-017-ERROR": "投票受付が終了しています。", "投票画面_投票-020-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "チャネル作成・更新画面_チャネル作成-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "チャネル作成・更新画面_チャネル作成-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "チャネル作成・更新画面_チャネル作成-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "チャネル作成・更新画面_チャネル作成-005-ERROR": "権限がありません。", "チャネル作成・更新画面_チャネル作成-006-ERROR": "DaoChannel Exception.", "チャネル作成・更新画面_チャネル作成-007-ERROR": "BrandSEQList Exception.", "チャネル作成・更新画面_チャネル作成-008-ERROR": "DaoID Varidation Error.", "チャネル作成・更新画面_チャネル作成-009-ERROR": "FunctionAuthority Exception.", "カテゴリー作成・更新画面_カテゴリー削除-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "カテゴリー作成・更新画面_カテゴリー削除-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "カテゴリー作成・更新画面_カテゴリー削除-004-ERROR": "情報の削除に失敗しました。再度実行して下さい。", "カテゴリー作成・更新画面_カテゴリー削除-005-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "カテゴリー作成・更新画面_カテゴリー削除-006-ERROR": "権限がありません。", "カテゴリー作成・更新画面_カテゴリー削除-007-ERROR": "DaoCategory Exception.", "カテゴリー作成・更新画面_カテゴリー削除-008-ERROR": "DaoChannel Exception.", "カテゴリー作成・更新画面_カテゴリー削除-009-ERROR": "DaoID Varidation Error.", "カテゴリー作成・更新画面_カテゴリー削除-010-ERROR": "FunctionAuthority Exception.", "カテゴリー作成・更新画面_カテゴリー更新-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "カテゴリー作成・更新画面_カテゴリー更新-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "カテゴリー作成・更新画面_カテゴリー更新-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "カテゴリー作成・更新画面_カテゴリー更新-005-ERROR": "権限がありません。", "カテゴリー作成・更新画面_カテゴリー更新-006-ERROR": "DaoCategory Exception.", "カテゴリー作成・更新画面_カテゴリー更新-007-ERROR": "DaoID Varidation Error.", "カテゴリー作成・更新画面_カテゴリー更新-008-ERROR": "FunctionAuthority Exception.", "チャネル作成・更新画面_チャネル削除-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "チャネル作成・更新画面_チャネル削除-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "チャネル作成・更新画面_チャネル削除-004-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "チャネル作成・更新画面_チャネル削除-005-ERROR": "権限がありません。", "チャネル作成・更新画面_チャネル削除-006-ERROR": "権限がありません。", "チャネル作成・更新画面_チャネル削除-007-ERROR": "DaoCategory Exception.", "チャネル作成・更新画面_チャネル削除-008-ERROR": "DaoID Varidation Error.", "チャネル作成・更新画面_チャネル削除-009-ERROR": "FunctionAuthority Exception.", "チャネル作成・更新画面_チャネル更新-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "チャネル作成・更新画面_チャネル更新-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "チャネル作成・更新画面_チャネル更新-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "チャネル作成・更新画面_チャネル更新-005-ERROR": "権限がありません。", "チャネル作成・更新画面_チャネル更新-006-ERROR": "DaoCategory Exception.", "チャネル作成・更新画面_チャネル更新-007-ERROR": "DaoID Varidation Error.", "チャネル作成・更新画面_チャネル更新-008-ERROR": "FunctionAuthority Exception.", "カテゴリー作成・更新画面_カテゴリー作成-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "カテゴリー作成・更新画面_カテゴリー作成-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "カテゴリー作成・更新画面_カテゴリー作成-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "カテゴリー作成・更新画面_カテゴリー作成-005-ERROR": "権限がありません。", "カテゴリー作成・更新画面_カテゴリー作成-006-ERROR": "DaoCategory Exception.", "カテゴリー作成・更新画面_カテゴリー作成-007-ERROR": "BrandSEQList Exception.", "カテゴリー作成・更新画面_カテゴリー作成-008-ERROR": "DaoID Varidation Error.", "カテゴリー作成・更新画面_カテゴリー作成-009-ERROR": "FunctionAuthority Exception.", "カテゴリー作成・更新画面_カテゴリー作成-010-ERROR": "DaoChannel Exception.", "カテゴリー・チャネル表示画面_一覧取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "カテゴリー・チャネル表示画面_一覧取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "カテゴリー・チャネル表示画面_一覧取得-004-ERROR": "DaoCategory Exception.", "カテゴリー・チャネル表示画面_一覧取得-005-ERROR": "DaoChannel Exception.", "カテゴリー・チャネル表示画面_一覧取得-006-ERROR": "DaoChatMention Exception.", "カテゴリー・チャネル表示画面_一覧取得-007-ERROR": "DaoID Varidation Error.", "カテゴリー・チャネル表示画面_一覧取得-008-ERROR": "CheerList Exception.", "カテゴリー・チャネル表示画面_メンション一覧取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "カテゴリー・チャネル表示画面_メンション一覧取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "カテゴリー・チャネル表示画面_メンション一覧取得-004-ERROR": "DaoCategory Exception.", "カテゴリー・チャネル表示画面_メンション一覧取得-005-ERROR": "DaoChannel Exception.", "カテゴリー・チャネル表示画面_メンション一覧取得-006-ERROR": "DaoChatMention Exception.", "カテゴリー・チャネル表示画面_メンション一覧取得-007-ERROR": "DaoID Varidation Error.", "カテゴリー・チャネル表示画面_メンション一覧取得-008-ERROR": "CheerList Exception.", "カテゴリー・チャネル表示画面_メンション一覧取得-009-ERROR": "DaoChatReactionCheer Exception.", "カテゴリー・チャネル表示画面_未読件数取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "カテゴリー・チャネル表示画面_未読件数取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "カテゴリー・チャネル表示画面_未読件数取得-004-ERROR": "DaoCategory Exception.", "カテゴリー・チャネル表示画面_未読件数取得-005-ERROR": "DaoChannel Exception.", "カテゴリー・チャネル表示画面_未読件数取得-006-ERROR": "DaoID Varidation Error.", "カテゴリー・チャネル表示画面_未読件数取得-007-ERROR": "DaoChatRead Exception.", "カテゴリー・チャネル表示画面_未読件数取得-008-ERROR": "DaoChat Exception.", "カテゴリー・チャネル表示画面_未読件数取得-009-ERROR": "DaoChatMention Exception.", "カテゴリー・チャネル表示画面_未読件数取得-010-ERROR": "DaoChatReactionCheer Exception.", "コミュニティチャット画面_スレッド情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "コミュニティチャット画面_スレッド情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "コミュニティチャット画面_スレッド情報取得-004-ERROR": "DaoCategory Exception.", "コミュニティチャット画面_スレッド情報取得-005-ERROR": "DaoChannel Exception.", "コミュニティチャット画面_スレッド情報取得-006-ERROR": "DaoThread Exception.", "コミュニティチャット画面_スレッド情報取得-007-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_スレッド情報取得-008-ERROR": "CheerList Exception.", "コミュニティチャット画面_スレッド情報取得-009-ERROR": "DaoChat Exception.", "コミュニティチャット画面_スレッド情報取得-010-ERROR": "DaoChatReactionCheer Exception.", "コミュニティチャット画面_スレッド情報取得-011-ERROR": "DaoParticipant Exception.", "コミュニティチャット画面_チャット情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "コミュニティチャット画面_チャット情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "コミュニティチャット画面_チャット情報取得-004-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_チャット情報取得-005-ERROR": "CheerList Exception.", "コミュニティチャット画面_チャット情報取得-006-ERROR": "DaoChat Exception.", "コミュニティチャット画面_チャット情報取得-007-ERROR": "DaoChatReactionCheer Exception.", "コミュニティチャット画面_チャット情報取得-008-ERROR": "DaoParticipant Exception.", "DAOトップ画面_話題の投稿情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "DAOトップ画面_話題の投稿情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "DAOトップ画面_話題の投稿情報取得-004-ERROR": "DaoID Varidation Error.", "DAOトップ画面_話題の投稿情報取得-005-ERROR": "DaoChannel Exception.", "DAOトップ画面_話題の投稿情報取得-006-ERROR": "DaoChat Exception.", "DAOトップ画面_話題の投稿情報取得-007-ERROR": "DaoParticipant Exception.", "DAOトップ画面_話題の投稿情報取得-008-ERROR": "DaoManagement Exception.", "DAOトップ画面_話題の投稿情報取得-009-ERROR": "DaoChatReactionCheer Exception.", "DAOトップ画面_参加状況取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "DAOトップ画面_参加状況取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "DAOトップ画面_参加状況取得-004-ERROR": "DaoParticipant Exception.", "DAOトップ画面_参加状況取得-005-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_リアクション画像取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "コミュニティチャット画面_リアクション画像取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "コミュニティチャット画面_リアクション画像取得-004-ERROR": "DaoReactionImage Exception.", "コミュニティチャット画面_リアクション画像取得-005-ERROR": "DaoID Varidation Error.", "home001-init-get-event-ticket-list-002-ERROR": "システムエラーが発生しました。", "home001-init-get-event-ticket-list-003-ERROR": "システムエラーが発生しました。", "DAO参加画面_参加者情報取得-001-INFO": "リクエストパラメータ: [${0}]", "DAO参加画面_参加者情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "DAO参加画面_参加者情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "DAO参加画面_参加者情報取得-004-ERROR": "DaoID Varidation Error.", "DAO参加画面_参加者情報取得-005-ERROR": "DaoManagement Exception.", "DAO参加画面_参加者情報取得-006-ERROR": "CheerList Exception.", "DAO参加画面_参加者登録-001-INFO": "リクエストパラメータ: [${0}]", "DAO参加画面_参加者登録-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "DAO参加画面_参加者登録-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "DAO参加画面_参加者登録-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "DAO参加画面_参加者登録-005-ERROR": "CheerList Exception.", "DAO参加画面_参加者登録-006-ERROR": "DaoParticipant Exception.", "DAO参加画面_参加者登録-007-ERROR": "DaoID Varidation Error.", "DAO参加画面_参加者登録-008-ERROR": "DaoManagement Exception.", "DAO参加画面_参加者登録-009-ERROR": "API Execution Exception.", "DAO参加画面_参加者登録-013-ERROR": "利用できません。", "DAO参加画面_参加者登録-015-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "DAO参加画面_参加者登録-017-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "DAOプロフィール表示画面_プロフィール情報取得-001-INFO": "リクエストパラメータ: [${0}]", "DAOプロフィール表示画面_プロフィール情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "DAOプロフィール表示画面_プロフィール情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "DAOプロフィール表示画面_プロフィール情報取得-004-ERROR": "権限がありません。", "DAOプロフィール表示画面_プロフィール情報取得-005-ERROR": "DaoParticipant Exception.", "DAOプロフィール表示画面_プロフィール情報取得-006-ERROR": "DaoID Varidation Error.", "DAOプロフィール表示画面_プロフィール情報取得-007-ERROR": "API Execution Exception.", "DAOプロフィール表示画面_プロフィール情報取得-008-ERROR": "DaoManagement Exception.", "DAOプロフィール表示画面_プロフィール情報取得-009-ERROR": "FunctionAuthority Exception.", "DAOプロフィール更新画面_プロフィール更新-001-INFO": "リクエストパラメータ: [${0}]", "DAOプロフィール更新画面_プロフィール更新-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "DAOプロフィール更新画面_プロフィール更新-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "DAOプロフィール更新画面_プロフィール更新-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "DAOプロフィール更新画面_プロフィール更新-005-ERROR": "DaoParticipant Exception.", "DAOプロフィール更新画面_プロフィール更新-006-ERROR": "DaoID Varidation Error.", "DAOプロフィール表示画面_退出-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "DAOプロフィール表示画面_退出-006-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "共通機能_URL取得-005-ERROR": "DaoVotingTheme Exception.", "共通機能_URL取得-006-ERROR": "DaoVotingCandidate Exception.", "共通機能_URL取得-007-ERROR": "BrandSEQList Exception.", "共通機能_URL取得-008-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_既読チャット情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "コミュニティチャット画面_既読チャット情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "コミュニティチャット画面_既読チャット情報取得-004-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_既読チャット情報取得-005-ERROR": "DaoChatRead Exception.", "コミュニティチャット画面_既読チャット更新-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "コミュニティチャット画面_既読チャット更新-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "コミュニティチャット画面_既読チャット更新-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "コミュニティチャット画面_既読チャット更新-005-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_既読チャット更新-006-ERROR": "DaoChatRead Exception.", "コミュニティチャット画面_チャット投稿登録-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "コミュニティチャット画面_チャット投稿登録-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "コミュニティチャット画面_チャット投稿登録-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "コミュニティチャット画面_チャット投稿登録-005-ERROR": "DaoThread Exception.", "コミュニティチャット画面_チャット投稿登録-006-ERROR": "DaoChat Exception.", "コミュニティチャット画面_チャット投稿登録-007-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_チャット投稿登録-008-ERROR": "BrandSEQList Exception.", "コミュニティチャット画面_チャット投稿登録-009-ERROR": "DaoChatMention Exception.", "コミュニティチャット画面_チャット投稿登録-010-ERROR": "CheerList Exception.", "コミュニティチャット画面_チャット投稿登録-011-ERROR": "DaoParticipant Exception.", "コミュニティチャット画面_チャット投稿更新-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "コミュニティチャット画面_チャット投稿更新-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "コミュニティチャット画面_チャット投稿更新-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "コミュニティチャット画面_チャット投稿更新-005-ERROR": "情報の削除に失敗しました。再度実行して下さい。", "コミュニティチャット画面_チャット投稿更新-006-ERROR": "権限がありません。", "コミュニティチャット画面_チャット投稿更新-007-ERROR": "DaoThread Exception.", "コミュニティチャット画面_チャット投稿更新-008-ERROR": "DaoChat Exception.", "コミュニティチャット画面_チャット投稿更新-009-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_チャット投稿更新-010-ERROR": "BrandSEQList Exception.", "コミュニティチャット画面_チャット投稿更新-011-ERROR": "DaoChatMention Exception.", "コミュニティチャット画面_チャット投稿更新-012-ERROR": "FunctionAuthority Exception.", "コミュニティチャット画面_チャット投稿更新-013-ERROR": "CheerList Exception.", "コミュニティチャット画面_チャット投稿更新-014-ERROR": "DaoParticipant Exception.", "コミュニティチャット画面_チャット投稿削除-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "コミュニティチャット画面_チャット投稿削除-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "コミュニティチャット画面_チャット投稿削除-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "コミュニティチャット画面_チャット投稿削除-005-ERROR": "権限がありません。", "コミュニティチャット画面_チャット投稿削除-006-ERROR": "DaoChat Exception.", "コミュニティチャット画面_チャット投稿削除-007-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_チャット投稿削除-008-ERROR": "BrandSEQList Exception.", "コミュニティチャット画面_チャット投稿削除-009-ERROR": "FunctionAuthority Exception.", "コミュニティチャット画面_チャット投稿削除-010-ERROR": "DaoThread Exception.", "コミュニティチャット画面_リアクション投稿削除-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "コミュニティチャット画面_リアクション投稿削除-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "コミュニティチャット画面_リアクション投稿削除-004-ERROR": "情報の削除に失敗しました。再度実行して下さい。", "コミュニティチャット画面_リアクション投稿削除-005-ERROR": "DaoChatReactionCheer Exception.", "コミュニティチャット画面_リアクション投稿削除-006-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_リアクション投稿削除-007-ERROR": "DaoChatReactionSummary Exception.", "コミュニティチャット画面_リアクション投稿登録-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "コミュニティチャット画面_リアクション投稿登録-003-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "コミュニティチャット画面_リアクション投稿登録-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "コミュニティチャット画面_リアクション投稿登録-005-ERROR": "DaoChatReactionCheer Exception.", "コミュニティチャット画面_リアクション投稿登録-006-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_リアクション投稿登録-007-ERROR": "BrandSEQList Exception.", "コミュニティチャット画面_リアクション投稿登録-008-ERROR": "DaoChat Exception.", "コミュニティチャット画面_リアクション投稿登録-009-ERROR": "DaoChatReactionSummary Exception.", "コミュニティチャット画面_参加者情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "コミュニティチャット画面_参加者情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "コミュニティチャット画面_参加者情報取得-004-ERROR": "DaoID Varidation Error.", "コミュニティチャット画面_参加者情報取得-005-ERROR": "DaoParticipant Exception.", "共通機能_買い物かご更新日時更新-001-INFO": "", "共通機能_買い物かご更新日時更新-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "共通機能_買い物かご更新日時更新-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "共通機能_買い物かご更新日時更新-005-ERROR": "", "共通機能_買い物かご更新日時更新-006-ERROR": "", "item001-init-get-itemlist-001-INFO": "", "item001-init-get-itemlist-002-ERROR": "システムエラーが発生しました。", "item001-init-get-itemlist-003-ERROR": "システムエラーが発生しました。", "item001-init-get-itemlist-004-ERROR": "システムエラーが発生しました。", "item001-init-get-itemlist-005-ERROR": "システムエラーが発生しました。", "item001-init-get-itemlist-006-ERROR": "システムエラーが発生しました。", "item002-init-get-itemquery-001-ERROR": "システムエラーが発生しました。", "item002-init-get-itemquery-002-ERROR": "システムエラーが発生しました。", "モバイルオーダー商品一覧画面_商品一覧取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "モバイルオーダー商品一覧画面_商品一覧取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "モバイルオーダー商品一覧画面_商品一覧取得-004-ERROR": "MobileOrderItem Exception.", "商品一覧画面_加盟店名取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "商品一覧画面_加盟店名取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "商品一覧画面_加盟店名取得-004-ERROR": "StoreList Exception.", "共通機能_カート情報存在確認-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "共通機能_カート情報存在確認-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "共通機能_カート情報存在確認-004-ERROR": "MobileOrderCartDetail Exception.", "モバイルオーダー共通機能_URL取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "モバイルオーダー共通機能_URL取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "モバイルオーダー共通機能_URL取得-004-ERROR": "Bucket Name Error.", "モバイルオーダー商品詳細画面_商品情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "モバイルオーダー商品詳細画面_商品情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "モバイルオーダー商品詳細画面_商品情報取得-004-ERROR": "MobileOrderItem Exception.", "モバイルオーダー商品詳細画面_商品情報取得-005-ERROR": "MobileOrderCart Exception.", "モバイルオーダー商品詳細画面_商品情報取得-006-ERROR": "MobileOrderCartDetail Exception.", "共通機能_カート情報更新-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "共通機能_カート情報更新-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "共通機能_カート情報更新-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "共通機能_カート情報更新-005-ERROR": "MobileOrderItem Exception.", "共通機能_カート情報更新-006-ERROR": "MobileOrderCart Exception.", "共通機能_カート情報更新-007-ERROR": "MobileOrderCartDetail Exception.", "共通機能_カート情報更新-008-ERROR": "BrandSEQList Exception.", "共通機能_カート情報更新-009-ERROR": "TransactWrite Exception.", "commonheader001-init-get-news-unread-flag-002-ERROR": "システムエラーが発生しました。", "commonheader001-init-get-news-unread-flag-003-ERROR": "システムエラーが発生しました。", "commonheader001-init-get-news-unread-flag-004-ERROR": "システムエラーが発生しました。", "commonheader001-init-get-news-unread-flag-005-ERROR": "システムエラーが発生しました。", "カート画面_商品削除-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "カート画面_商品削除-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "カート画面_商品削除-004-ERROR": "情報の削除に失敗しました。再度実行して下さい。", "カート画面_商品削除-005-ERROR": "", "カート画面_商品削除-006-ERROR": "", "カート画面_カート情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "カート画面_カート情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "カート画面_カート情報取得-004-ERROR": "", "カート画面_カート情報取得-005-ERROR": "", "カート画面_カート情報取得-006-ERROR": "", "カート画面_注文確定前確認-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "カート画面_注文確定前確認-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "カート画面_注文確定前確認-004-ERROR": "", "カート画面_注文確定前確認-005-ERROR": "", "カート画面_注文確定前確認-006-ERROR": "", "カート画面_注文確定-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "カート画面_注文確定-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "カート画面_注文確定-004-ERROR": "情報の設定に失敗しました。再度実行して下さい。", "カート画面_注文確定-005-ERROR": "", "カート画面_注文確定-006-ERROR": "", "カート画面_注文確定-007-ERROR": "", "カート画面_注文確定-008-ERROR": "", "カート画面_注文確定-009-ERROR": "カートの内容が変更されています。\n画面を更新して下さい。", "カート画面_注文確定-010-ERROR": "在庫が不足している商品があります。\n商品情報を確認してください。", "top001-init-get-info-002-ERROR": "リクエストパラメータが異常です。", "top001-init-get-info-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "top001-init-get-info-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "top001-init-get-info-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "top001-init-get-storelist-002-ERROR": "システムエラーが発生しました。", "top001-init-get-storelist-003-ERROR": "システムエラーが発生しました。", "top001-init-get-storelist-004-ERROR": "システムエラーが発生しました。", "top001-init-get-eventlist-002-ERROR": "システムエラーが発生しました。", "top001-init-get-eventlist-003-ERROR": "システムエラーが発生しました。", "top001-init-get-eventlist-004-ERROR": "システムエラーが発生しました。", "top001-init-get-stamprallylist-002-ERROR": "システムエラーが発生しました。", "top001-init-get-stamprallylist-003-ERROR": "システムエラーが発生しました。", "top001-init-get-stamprallylist-004-ERROR": "システムエラーが発生しました。", "top001-init-get-missionlist-002-ERROR": "システムエラーが発生しました。", "top001-init-get-missionlist-003-ERROR": "システムエラーが発生しました。", "top001-init-get-missionlist-004-ERROR": "システムエラーが発生しました。", "top001-init-get-ecitemlist-002-ERROR": "システムエラーが発生しました。", "top001-init-get-ecitemlist-003-ERROR": "システムエラーが発生しました。", "top001-init-get-ecitemlist-004-ERROR": "システムエラーが発生しました。", "top001-init-get-ecitemlist-005-ERROR": "システムエラーが発生しました。", "top001-init-get-nftcontentlist-002-ERROR": "システムエラーが発生しました。", "top001-init-get-nftcontentlist-003-ERROR": "システムエラーが発生しました。", "top001-init-get-nftcontentlist-004-ERROR": "システムエラーが発生しました。", "top001-init-get-daolist-002-ERROR": "システムエラーが発生しました。", "top001-init-get-daolist-003-ERROR": "システムエラーが発生しました。", "top001-init-get-daolist-004-ERROR": "システムエラーが発生しました。", "top001-init-get-couponlist-001-INFO": "リクエストパラメータ：[${0}]", "top001-init-get-couponlist-002-ERROR": "システムエラーが発生しました。", "top001-init-get-couponlist-003-ERROR": "システムエラーが発生しました。", "top001-init-get-couponlist-004-ERROR": "システムエラーが発生しました。", "top001-init-get-couponlist-005-ERROR": "システムエラーが発生しました。", "brand001-init-get-brand-list-002-ERROR": "システムエラーが発生しました。", "brand001-init-get-brand-list-003-ERROR": "システムエラーが発生しました。", "brand001-init-get-brand-list-004-ERROR": "システムエラーが発生しました。", "brand001-init-get-brand-list-005-ERROR": "システムエラーが発生しました。", "brand001-init-get-brand-list-006-ERROR": "システムエラーが発生しました。", "brand001-init-get-brand-list-007-ERROR": "システムエラーが発生しました。", "brand001-init-get-brand-list-008-ERROR": "システムエラーが発生しました。", "brand001-init-get-brand-list-009-ERROR": "システムエラーが発生しました。", "brand001-init-get-brand-list-010-ERROR": "システムエラーが発生しました。", "brand001-init-get-brand-list-011-ERROR": "システムエラーが発生しました。", "brand001-init-get-brand-list-012-ERROR": "システムエラーが発生しました。", "brand002-main-update-brand-002-ERROR": "システムエラーが発生しました。", "brand002-main-update-brand-003-ERROR": "システムエラーが発生しました。", "brand002-main-update-brand-004-WARNING": "システムエラーが発生しました。", "brand002-main-update-brand-005-ERROR": "システムエラーが発生しました。", "wallet001-init-get-limited-store-list-002-ERROR": "システムエラーが発生しました。", "wallet001-init-get-limited-store-list-003-ERROR": "システムエラーが発生しました。", "wallet001-init-get-limited-store-list-004-ERROR": "システムエラーが発生しました。", "wallet001-init-get-limited-store-list-005-ERROR": "システムエラーが発生しました。", "wallet001-init-get-limited-store-list-006-ERROR": "システムエラーが発生しました。", "wallet001-init-get-limited-store-list-007-ERROR": "システムエラーが発生しました。", "wallet001-init-get-limited-store-list-008-ERROR": "システムエラーが発生しました。", "wallet001-init-get-limited-store-list-009-ERROR": "システムエラーが発生しました。", "wallet001-init-get-banner-list-002-ERROR": "システムエラーが発生しました。", "wallet001-init-get-coin-001-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "wallet001-init-get-coin-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "wallet001-init-get-coin-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "wallet001-init-get-coin-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "wallet001-init-get-coin-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "wallet001-init-get-coin-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "wallet001-init-get-coin-007-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "wallet001-init-get-coupon-list-002-ERROR": "システムエラーが発生しました。", "wallet001-init-get-coupon-list-003-ERROR": "システムエラーが発生しました。", "wallet001-init-get-coupon-list-004-ERROR": "システムエラーが発生しました。", "wallet001-init-get-coupon-list-005-ERROR": "システムエラーが発生しました。", "wallet001-init-get-coupon-list-006-ERROR": "システムエラーが発生しました。", "wallet001-init-get-coupon-list-007-ERROR": "システムエラーが発生しました。", "wallet001-init-get-coupon-list-008-ERROR": "システムエラーが発生しました。", "wallet001-init-get-coupon-list-009-ERROR": "システムエラーが発生しました。", "wallet001-init-get-news-unread-latest-001-ERROR": "リクエストパラメータが異常です。", "wallet001-init-get-news-unread-latest-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-news-unread-latest-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-news-unread-latest-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-news-unread-latest-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-userinfo-001-ERROR": "リクエストパラメータが異常です。", "wallet001-init-get-userinfo-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-userinfo-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-main-get-payableflag-001-INFO": "リクエストパラメータ", "wallet001-main-get-payableflag-002-ERROR": "リクエストパラメータが異常です。", "wallet001-main-get-payableflag-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-main-get-payableflag-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-main-get-chargeableflag-001-INFO": "リクエストパラメータ", "wallet001-main-get-chargeableflag-002-ERROR": "リクエストパラメータが異常です。", "wallet001-main-get-chargeableflag-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-main-get-chargeableflag-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "DAOトップ画面_DAO一覧取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "DAOトップ画面_DAO一覧取得-003-ERROR": "Get DaoManagement Exception.", "DAO概要画面_DAO情報取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "DAO概要画面_DAO情報取得-003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "DAO概要画面_DAO情報取得-004-ERROR": "DaoID Varidation Error.", "DAO概要画面_DAO情報取得-005-ERROR": "DaoManagement Exception.", "DAO概要画面_DAO情報取得-006-ERROR": "CheerList Exception.", "共通機能_参加状況取得-002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。.", "投票テーマ管理画面_投票テーマ削除 -002-ERROR": "情報の取得に失敗しました。ブラウザ画面を更新して下さい。", "投票テーマ管理画面_投票テーマ削除 -003-ERROR": "情報の取得に失敗しました。再度実行して下さい。", "投票テーマ管理画面_投票テーマ削除 -004-ERROR": "情報の削除に失敗しました。再度実行して下さい。", "投票テーマ管理画面_投票テーマ削除 -005-ERROR": "権限がありません。", "common-init-get-userinfo-001-ERROR": "リクエストパラメータが異常です。", "common-init-get-userinfo-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "common-init-get-userinfo-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "deleteaccount002-main-delete-account-001-ERROR": "リクエストパラメータが異常です。", "deleteaccount002-main-delete-account-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "deleteaccount002-main-delete-account-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "deleteaccount002-main-delete-account-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "deleteaccount002-main-delete-account-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "deleteaccount002-main-delete-account-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "deleteaccount002-main-delete-account-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "deleteaccount002-main-delete-account-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "qrcodepresent001-init-create-transfercode-002-ERROR": "データの取得に失敗しました。", "qrcodepresent001-init-create-transfercode-003-ERROR": "データの取得に失敗しました。", "qrcodepresent001-init-get-result-002-ERROR": "リクエストパラメータが異常です。", "qrcodepresent001-init-get-result-003-ERROR": "データの取得に失敗しました。", "qrcodepresent001-init-get-result-004-ERROR": "データの取得に失敗しました。", "qrcodepresent001-init-get-result-005-ERROR": "データの取得に失敗しました。", "qrcodepresent001-init-get-result-006-ERROR": "データの取得に失敗しました。", "chargeintbnkpay002-init-get-bankname-001-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay002-main-register-bankaccount-002-ERROR": "リクエストパラメータが異常です。", "chargeintbnkpay002-main-register-bankaccount-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay002-main-register-bankaccount-004-ERROR": "銀口座登録に失敗しました\n再度お試しください", "chargeintbnkpay002-main-register-bankaccount-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay002-main-register-bankaccount-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay002-main-register-bankaccount-007-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay002-main-register-bankaccount-008-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay002-main-register-bankaccount-009-ERROR": "銀口座登録に失敗しました\n再度お試しください", "chargeintbnkpay002-main-register-bankaccount-010-ERROR": "銀口座登録に失敗しました\n再度お試しください", "chargeintbnkpay002-main-register-bankaccount-011-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay002-main-register-bankaccount-012-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay002-main-register-bankaccount-013-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeselect001-main-get-bankpayregisterflag-001-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay005-init-get-chargelimit-002-ERROR": "リクエストパラメータが異常です。", "chargeintbnkpay005-init-get-chargelimit-003-ERROR": "フォーマットが不正です。", "chargeintbnkpay005-init-get-chargelimit-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay005-init-get-chargelimit-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay005-init-get-bankinfo-001-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay005-init-get-bankinfo-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay005-init-get-bankinfo-003-ERROR": "口座が登録されていません。", "chargeintbnkpay005-init-get-bankinfo-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay005-init-get-bankinfo-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay005-init-get-bankinfo-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay005-init-get-bankinfo-007-ERROR": "他のユーザーが同一の口座を登録した恐れがあります。口座登録をやり直してください。", "chargeintbnkpay005-init-get-bankinfo-008-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay005-init-get-bankinfo-009-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay005-init-get-bankinfo-010-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay006-main-execute-charge-002-ERROR": "リクエストパラメータが異常です。", "chargeintbnkpay006-main-execute-charge-003-ERROR": "口座が登録されていません。", "chargeintbnkpay006-main-execute-charge-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay006-main-execute-charge-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay006-main-execute-charge-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay006-main-execute-charge-007-ERROR": "チャージに失敗しました。", "chargeintbnkpay006-main-execute-charge-008-ERROR": "チャージに失敗しました。", "chargeintbnkpay006-main-execute-charge-009-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay006-main-execute-charge-010-ERROR": "1分後に再度お試しください", "chargeintbnkpay006-main-execute-charge-011-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay008-init-get-bankinfo-001-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay008-init-get-bankinfo-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay008-init-get-bankinfo-003-ERROR": "口座が登録されていません。", "chargeintbnkpay008-init-get-bankinfo-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay008-init-get-bankinfo-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay008-init-get-bankinfo-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay008-init-get-bankinfo-007-ERROR": "他のユーザーが同一の口座を登録した恐れがあります。口座登録をやり直してください。", "chargeintbnkpay008-init-get-bankinfo-008-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay008-init-get-bankinfo-009-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay008-init-get-bankinfo-010-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay008-main-delete-bankaccount-001-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay008-main-delete-bankaccount-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay008-main-delete-bankaccount-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay008-main-delete-bankaccount-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-init-get-info-002-ERROR": "リクエストパラメータが異常です", "chargeintbnkpay009-init-get-info-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-init-get-info-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-init-get-info-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-init-get-info-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-init-get-info-007-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-init-get-info-008-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-init-get-info-009-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-init-get-info-010-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-main-register-bankaccount-002-ERROR": "リクエストパラメータが異常です。", "chargeintbnkpay009-main-register-bankaccount-003-ERROR": "銀口座登録に失敗しました。再度お試しください。", "chargeintbnkpay009-main-register-bankaccount-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-main-register-bankaccount-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-main-register-bankaccount-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-main-register-bankaccount-007-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-main-register-bankaccount-008-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-main-register-bankaccount-009-ERROR": "銀口座登録に失敗しました。再度お試しください。", "chargeintbnkpay009-main-register-bankaccount-010-ERROR": "銀口座登録に失敗しました。再度お試しください。", "chargeintbnkpay009-main-register-bankaccount-011-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-main-register-bankaccount-012-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "chargeintbnkpay009-main-register-bankaccount-013-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "wallet001-init-get-recommendation-popup-detail-001-INFO": "リクエストパラメータが異常です。", "wallet001-init-get-recommendation-popup-detail-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-009-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-010-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-011-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-012-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-013-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-014-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-015-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-016-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-017-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-018-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "wallet001-init-get-recommendation-popup-detail-019-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "ideadao": {"ideadao001": {"behavior": {"profile": "プロフィール", "chat": "チャット", "task": "タスク", "dao_edit": "DAO編集", "task_create": "タスク登録", "member": "メンバー"}}, "ideadao009": {"screen_name": "タスク一覧", "show_more_item": "次の{0}件を表示", "data_task_not_found": "タスクがありません", "status": {"1": "募集中", "2": "未着手", "3": "進行中", "4": "完了", "5": "承認済"}, "task_class": {"1": "直接作業", "2": "支援"}, "create_time": "作成日 :", "update_time": "更新日 :", "reward_amount_label": "報酬", "unnamed": "担当者なし", "create": "新規作成"}, "ideadao010": {"screen_title": "タスク登録", "name_label": "タスク名", "name_placeholder": "入力してください", "up_to_characters": "{0}文字以内", "description_label": "タスク内容", "description_placeholder": "入力してください", "reward_amount_label": "報酬設定", "task": "タスク", "token": "トークン", "task_class_label": "タスク種類", "task_class_work_directly": "直接作業", "task_class_support": "支援（資金・場所・物資の提供）", "back_button": "戻る", "task_create_confirm_btn": "確認画面へ", "task_create_button": "タスク登録"}, "ideadao011": {"screen_title": "タスク詳細", "step": [{"text": "募集中"}, {"text": "未着手"}, {"text": "進行中"}, {"text": "完了"}, {"text": "承認済"}], "overview_tab_label": "概要", "file_tab_label": "ファイル", "creater_label": "タスク登録者", "assignee_label": "タスク実施者", "action_button_label": "アクションボタン", "assign_button": "担当する", "start_button": "着手する", "request_approval_button": "承認を依頼する", "approval_button": "承認する", "denial_button": "承認しない", "upload_button": "アップロードする", "upload_success_status": "アップロードしました。", "delete_success_status": "削除しました。", "attachment_empty": "ファイルがありません", "delete_label": "削除", "download_label": "ダウンロード", "confirm_delete_message": "削除します。よろしいですか？", "no_action": "現在必要なアクションはありません", "waiting_approve": "タスク登録者の承認待ちです", "no_assignee": "担当者なし", "yes": "はい", "no": "いいえ", "organizer": "主催者"}}, "タスク登録-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "タスク登録-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "タスク登録-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "タスク登録-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "タスク登録-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "タスク登録-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "タスク登録-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "タスク登録-017-ERROR": "権限がありません。", "coupon001-init-get-coupon-list-007-ERROR": "システムエラーが発生しました。", "coupon001-init-get-coupon-list-008-ERROR": "システムエラーが発生しました。", "coupon001-init-get-coupon-list-009-ERROR": "システムエラーが発生しました。", "coupon001-init-get-coupon-list-010-ERROR": "システムエラーが発生しました。", "coupon001-init-get-coupon-list-011-ERROR": "システムエラーが発生しました。", "coupon001-init-get-coupon-list-012-ERROR": "システムエラーが発生しました。", "coupon001-init-get-coupon-list-013-ERROR": "システムエラーが発生しました。", "coupon001-init-get-coupon-list-014-ERROR": "システムエラーが発生しました。", "coupon004-main-use-coupon-006-ERROR": "システムエラーが発生しました。", "coupon004-main-use-coupon-007-ERROR": "システムエラーが発生しました。", "coupon004-main-use-coupon-008-ERROR": "システムエラーが発生しました。", "coupon004-main-use-coupon-009-INFO": "利用できる期間外です。", "coupon004-main-use-coupon-010-INFO": "利用できないクーポンになりました", "coupon004-main-use-coupon-011-ERROR": "システムエラーが発生しました。", "coupon004-main-use-coupon-012-INFO": "利用できないクーポンになりました", "coupon004-main-use-coupon-013-ERROR": "システムエラーが発生しました。", "coupon004-main-use-coupon-014-ERROR": "システムエラーが発生しました。", "coupon004-main-use-coupon-015-ERROR": "システムエラーが発生しました。", "ideadao009-init-get-task-list-002-ERROR": ":システムエラーが発生しました。", "ideadao009-init-get-task-list-003-ERROR": ":システムエラーが発生しました。", "ideadao009-init-get-task-list-004-ERROR": ":システムエラーが発生しました。", "ideadao009-init-get-task-list-005-ERROR": ":システムエラーが発生しました。", "ideadao009-init-get-task-list-006-ERROR": ":システムエラーが発生しました。", "ideadao009-init-get-task-list-007-ERROR": "システムエラーが発生しました。", "ideadao011-init-get-taskinfodetail-002-ERROR": "システムエラーが発生しました。", "ideadao011-init-get-taskinfodetail-003-ERROR": "システムエラーが発生しました。", "ideadao011-init-get-taskinfodetail-004-ERROR": "システムエラーが発生しました。", "ideadao011-init-get-taskinfodetail-005-ERROR": "システムエラーが発生しました。", "ideadao011-init-get-taskinfodetail-006-ERROR": "システムエラーが発生しました。", "ideadao011-init-get-taskinfodetail-007-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_未着手-002-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_未着手-003-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_進行中-002-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_進行中-003-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_進行中-004-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_完了-002-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_完了-003-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_完了-004-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_承認済-002-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_承認済-003-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_承認済-004-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_承認済-005-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_承認済-006-ERROR": "システムエラーが発生しました。", "タスク詳細画面_ステータス更新_承認済-014-ERROR": "権限がありません。", "ideadao007-init-get-members-list-001-INFO": "リクエストパラメータ", "ideadao007-init-get-members-list-002-ERROR": "システムエラーが発生しました。", "ideadao007-init-get-members-list-003-ERROR": "システムエラーが発生しました。", "ideadao007-init-get-members-list-004-ERROR": "システムエラーが発生しました。", "ideadao007-init-get-members-list-005-ERROR": "ParticipantStatus Varidation Error.", "ideadao007-init-get-members-list-006-ERROR": "Get DaoParticipant Exception.", "ideadao007-init-get-members-list-007-ERROR": "Get IdeaDaoManagement Exception.", "survey001-init-get-survey-list-001-ERROR": "システムエラーが発生しました。", "survey001-init-get-survey-list-002-ERROR": "システムエラーが発生しました。", "survey001-init-get-survey-list-003-ERROR": "システムエラーが発生しました。", "survey001-init-get-survey-list-004-ERROR": "システムエラーが発生しました。", "survey001-init-get-survey-list-005-ERROR": "システムエラーが発生しました。", "survey002-init-get-survey-list-001-ERROR": "リクエストパラメータが異常です。", "survey002-init-get-survey-list-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "survey002-init-get-survey-list-003-ERROR": "アンケートの受付期間外です。", "survey002-init-get-survey-list-004-ERROR": "アンケートの受付期間外です。", "survey002-init-get-survey-list-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "survey002-init-get-survey-list-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "survey002-init-get-survey-list-007-ERROR": "データが見つかりませんでした。しばらく時間をおいてから再度お試しください。", "survey002-init-get-survey-list-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "survey002-init-get-survey-list-009-ERROR": "既に回答を受付済です。", "survey002-init-get-survey-list-010-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "survey003-main-send-answer-001-ERROR": "リクエストパラメータが異常です。", "survey003-main-send-answer-002-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "survey003-main-send-answer-003-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "survey003-main-send-answer-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "survey003-main-send-answer-005-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "survey003-main-send-answer-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "survey003-main-send-answer-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "survey003-main-send-answer-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "survey003-main-send-answer-009-ERROR": "残高上限超過です。", "common-get-medalinfo-001-INFO": "リクエストパラメータ：[${0}]", "common-get-medalinfo-002-ERROR": "リクエストパラメータが異常です。", "common-get-medalinfo-003-ERROR": "Get BUCKET_URI Exception.", "common-get-medalinfo-004-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "common-get-medalinfo-005-ERROR": "Get MedalServiceList Exception.", "common-get-medalinfo-006-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "common-get-medalinfo-007-ERROR": "Get balance Exception.", "common-get-medalinfo-008-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "common-get-medalinfo-009-ERROR": "Get BlockchainMedalConfig Exception.", "common-get-medalinfo-010-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "common-transfer-main-execute-payment-001-INFO": "リクエストパラメータ：[${0}]", "common-transfer-main-execute-payment-002-ERROR": "リクエストパラメータが不正です。", "common-transfer-main-execute-payment-003-ERROR": "Call Lambda Error", "common-transfer-main-get-userinfo-001-INFO": "リクエストパラメータ：[${0}]", "common-transfer-main-get-userinfo-002-ERROR": "リクエストパラメータが異常です", "common-transfer-main-get-userinfo-003-ERROR": "Get QRAuthCodeList Exception.", "common-transfer-main-get-userinfo-004-ERROR": "システムエラーが発生しました\nしばらく時間をおいてから再度お試しください123", "common-transfer-main-get-userinfo-005-ERROR": "QRAuthCode NG.", "common-transfer-main-get-userinfo-006-INFO": "QRコードが不正です\n再度お試しください", "common-transfer-main-get-userinfo-007-ERROR": "QRAuthCode Timeout.", "common-transfer-main-get-userinfo-008-INFO": "QRコードの有効期限が過ぎています\n再度お試しください", "common-transfer-main-get-userinfo-009-ERROR": "Get CheerList Exception.", "common-transfer-main-get-userinfo-010-ERROR": "システムエラーが発生しました", "common-transfer-main-get-userinfo-011-ERROR": "Get CheerList Exception.", "common-transfer-main-get-userinfo-012-ERROR": "QRコードが不正です\n再度お試しください", "common-transfer-main-get-userinfo-013-ERROR": "Get GetOrderID Exception.", "common-transfer-main-get-orderid-001-INFO": "リクエストパラメータ：[${0}]", "common-transfer-main-get-orderid-002-ERROR": "リクエストパラメータが異常です。", "common-transfer-main-get-orderid-003-ERROR": "Get GetOrderID Exception.", "transfer007-init-get-coin-001-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-002-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-003-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-004-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-005-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-006-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-007-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "transfer007-init-get-coin-008-INFO": "リクエストパラメータ：[${0}]", "transfer007-init-get-coin-009-ERROR": "リクエストパラメータが異常です", "transfer007-init-get-coin-010-ERROR": "システムエラーが発生しました。時間をおいて再度実行してください。", "common-mynumber-init-get-mynasetting-noauth-001-INFO": "リクエストパラメータ：[${0}]", "common-mynumber-init-get-mynasetting-noauth-002-ERROR": "リクエストパラメータが異常です", "common-mynumber-init-get-mynasetting-noauth-003-ERROR": "Get BrandList Exception.", "common-mynumber-init-get-mynasetting-noauth-004-ERROR": "システムエラーが発生しましたしばらく時間をおいてから再度お試しください", "mynumber002-init-get-usercert-stub-001-INFO": "リクエストパラメータ：[${0}]", "mynumber002-init-get-usercert-stub-002-ERROR": "リクエストパラメータが異常です。", "mynumber002-init-get-usercert-stub-003-ERROR": "Get BrandList Exception.", "mynumber002-init-get-usercert-stub-004-ERROR": "システムエラーが発生しました。", "mynumber002-init-get-usercert-stub-005-INFO": "APIの実行が完了しました。", "mynumber002-init-get-usercert-stub-006-ERROR": "Get BUCKET_URI Exception.", "mynumber002-init-get-usercert-stub-007-ERROR": "システムエラーが発生しました。しばらく時間をおいてから再度お試しください。", "mynumber002-init-get-usercert-stub-008-ERROR": "Set SessionInfo Exception.", "mynumber002-init-get-usercert-stub-009-ERROR": "セッション情報の登録に失敗しました。", "mynumber002-init-get-usercert-001-INFO": "リクエストパラメータ：[${0}]", "mynumber002-init-get-usercert-002-ERROR": "リクエストパラメータが異常です。", "mynumber002-init-get-usercert-003-ERROR": "Get BrandList Exception.", "mynumber002-init-get-usercert-004-ERROR": "システムエラーが発生しました。", "mynumber002-init-get-usercert-005-INFO": "APIの実行が完了しました。", "読取内容確認_MCAS取得-001-INFO": "リクエストパラメータ: [${0}]", "読取内容確認_MCAS取得-002-ERROR": "リクエストパラメータが不正です。", "読取内容確認_MCAS取得-003-ERROR": "Get Basic4Info Exception.", "読取内容確認_MCAS取得-004-ERROR": "基本4情報の取得に失敗しました。", "読取内容確認_MCAS取得-005-ERROR": "Get Basic4Info Exception.", "読取内容確認_MCAS取得-006-ERROR": "基本4情報の取得に失敗しました。", "読取内容確認_MCAS更新-001-INFO": "リクエストパラメータ: [${0}]", "読取内容確認_MCAS更新-002-ERROR": "リクエストパラメータが不正です。", "読取内容確認_MCAS更新-003-ERROR": "Get elastiCache Exception.", "読取内容確認_MCAS更新-004-ERROR": "セッション情報の取得に失敗しました。", "読取内容確認_MCAS更新-005-ERROR": "Get elastiCache Exception.", "読取内容確認_MCAS更新-006-ERROR": "セッション情報の取得に失敗しました。", "読取内容確認_MCAS更新-007-ERROR": "Get elastiCache Exception.", "読取内容確認_MCAS更新-008-ERROR": "ユーザ情報の取得に失敗しました。", "読取内容確認_MCAS更新-009-ERROR": "EnduserId Exception.", "読取内容確認_MCAS更新-010-ERROR": "登録されているカード情報と異なっています。", "読取内容確認_MCAS更新-011-ERROR": "Update IdeaDaoTaskManagement Exception.", "読取内容確認_MCAS更新-012-ERROR": "データの更新に失敗しました。", "settingtop001-main-unlink-line-001-ERROR": "システムエラーが発生しました\n時間をおいて再度実行してください", "settingtop001-main-unlink-line-002-ERROR": "システムエラーが発生しました\n時間をおいて再度実行してください", "settingtop001-main-unlink-line-003-ERROR": "システムエラーが発生しました\n時間をおいて再度実行してください", "settingtop001-main-unlink-line-004-ERROR": "システムエラーが発生しました\n時間をおいて再度実行してください", "settingtop001-main-unlink-line-005-ERROR": "システムエラーが発生しました\n時間をおいて再度実行してください", "settingtop001-main-get-line-token-001-ERROR": "システムエラーが発生しました\n時間をおいて再度実行してください", "common-init-get-oidc-002-ERROR": "リクエストパラメータが異常です", "common-init-get-oidc-003-ERROR": "システムエラーが発生しました\n時間をおいて再度実行してください", "common-cognito-id-merge-001-ERROR": "システムエラーが発生しました時間をおいて再度実行してください", "common-cognito-id-merge-002-ERROR": "システムエラーが発生しました時間をおいて再度実行してください", "common-cognito-id-merge-003-ERROR": "システムエラーが発生しました時間をおいて再度実行してください", "common-cognito-id-merge-004-ERROR": "Link account fails", "common-cognito-id-merge-005-ERROR": "Link account fails", "common-cognito-id-merge-006-ERROR": "Link account fails", "common-cognito-id-merge-007-ERROR": "Link account fails", "common-cognito-id-merge-008-ERROR": "Update AccessTokenLine and RefreshTokenLine failed"}