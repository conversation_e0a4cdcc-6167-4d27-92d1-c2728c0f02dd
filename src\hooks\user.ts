import { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Home001InitGetNewsUnreadFlagResult } from '../models/home001-init-get-news-unread-flag';
import { RootState } from '../redux';
import { userActions } from '../redux/slices/user';
import { UserInfo, UserState } from '../redux/states/user';

/**
 * UseUser
 */
interface UseUser extends UserState {
    updateUserInfo: (payload: Partial<UserInfo>) => void;
    updateUnreadFlag: (payload: Home001InitGetNewsUnreadFlagResult) => void;
    updateUserId: (payload: { UserID: string }) => void;
    updateDisplayID: (payload: { DisplayID: string }) => void;
    updateLineProfile: (payload: { username?: string }) => void;
    updateSelectedMedalServiceId: (payload: { MedalServiceID?: string }) => void;
    clearLineProfile: () => void;
}

/**
 * useUser hook
 */
const useUser = (): UseUser => {
    const state = useSelector((root: RootState) => root.userReducer);
    const dispatch = useDispatch();

    /**
     * updateUserInfo
     * @param payload Partial<UserInfo>
     */
    const updateUserInfo = useCallback((payload: Partial<UserInfo>) => {
        dispatch(userActions.updateUserInfo(payload));
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    /**
     * updateUnreadFlag
     * @param payload boolean
     */
    const updateUnreadFlag = useCallback(
        (payload: Home001InitGetNewsUnreadFlagResult) => {
            dispatch(userActions.updateHaveUnreadNews(payload));
        },
        [dispatch]
    );

    /**
     * updateUserId
     * @param payload { UserID: string }
     */
    const updateUserId = useCallback(
        (payload: { UserID: string }) => {
            dispatch(userActions.updateUserId(payload));
        },
        [dispatch]
    );

    /**
     * updateDisplayID
     * @param payload { DisplayID: string }
     */
    const updateDisplayID = useCallback(
        (payload: { DisplayID: string }) => {
            dispatch(userActions.updateDisplayID(payload));
        },
        [dispatch]
    );

    /**
     * updateLineProfile
     * @param payload { userId: string }
     */
    const updateLineProfile = useCallback(
        (payload: { username?: string }) => {
            dispatch(userActions.updateLineProfile(payload));
        },
        [dispatch]
    );
    /**
     * updateSelectedMedalServiceId
     * @param payload {MedalServiceID:string}
     */
    const updateSelectedMedalServiceId = useCallback(
        (payload: { MedalServiceID?: string }) => {
            dispatch(userActions.updateSelectedMedalServiceId(payload));
        },
        [dispatch]
    );

    /**
     * clearLineProfile
     */
    const clearLineProfile = useCallback(() => {
        dispatch(userActions.clearLineProfile());
    }, [dispatch]);

    return useMemo(
        () => ({
            ...state,
            updateUserInfo,
            updateUnreadFlag,
            updateUserId,
            updateDisplayID,
            updateLineProfile,
            updateSelectedMedalServiceId,
            clearLineProfile,
        }),
        [
            state,
            updateUserInfo,
            updateUnreadFlag,
            updateUserId,
            updateDisplayID,
            updateLineProfile,
            updateSelectedMedalServiceId,
            clearLineProfile,
        ]
    );
};

export default useUser;
