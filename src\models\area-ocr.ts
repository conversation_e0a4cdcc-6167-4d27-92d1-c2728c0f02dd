import { CommonCallback } from './common';

/**
 * AreaOCRItem
 */
export interface AreaOCRItem {
    Text: string;
}

/**
 * AreaOCRList
 */
export interface AreaOCRList {
    Blocks: AreaOCRItem[];
}

/**
 * AreaOCRModel
 */
export interface AreaOCRModel extends CommonCallback {
    SdkBytes: string;
}

/**
 * AreaOCRResponse
 */
export interface AreaOCRResponse {
    // ステータス(0:正常,0以外:異常)
    status: number;
    // 返却結果
    result: AreaOCRList;
    Message?: string;
}
