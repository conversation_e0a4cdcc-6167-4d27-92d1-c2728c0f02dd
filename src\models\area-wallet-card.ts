import { CommonCallback } from './common';

/**
 * AreaWalletCardModel
 */
export interface AreaWalletCardModel extends CommonCallback {
    // ブランドID
    BrandID?: string;
    // 応援者ID
    CheerID: number;
    // BuyTicketAmount
    ProductID: number;
    // Price
    Price: number;
    TotalAmount?: number;
    AreaCouponID?: number;
    PaymentType?: string;
}

/**
 * PaymentResult
 */
export interface AreaWalletCardResponse {
    // ResultCode
    ResultCode: string;
    // Status
    Status: string;
    // Message
    Message: string;
    // OrderID
    OrderID: string;
    // PaymentKey
    PaymentKey: string;
    // PopClientKey
    PopClientKey: string;
}
