import { CommonCallback } from './common';

/**
 * AreaWalletCashReceiveRealModel
 */
export interface AreaWalletCashReceiveRealModel extends CommonCallback {
    // ブランドID
    BrandID: string;
    // ユーザID
    FromCheerID?: number;
    // ユーザID
    ToCheerID: number;
    // 商品券利用額
    TicketAmount?: number;
    // ブロックチェーン管理タイプ
    WalletType: string;
    // 商品ID
    ProductID?: number;
    // MailAddress of ToCheerID
    MailAddress?: string;
    // 応援者ID
    CheerID?: number;
    SerialCode?: string;
    OrderID?: string;
    Price?: number;
    AreaCouponID?: number;
    ProductAmount?: number;
    EventPublicCode?: string;
    // 共通ポイントID
    CommonPointID?: number;
    CashAmount?: number;
    // 加盟店コード
    StoreID?: number;
}

/**
 * AreaWalletCashReceiveRealResponse
 */
export interface AreaWalletCashReceiveRealResponse {
    // ResultStatus
    ResultStatus: number;
    // OrderID
    OrderID?: string;
    // OrderStatus
    OrderStatus?: number;
    // ChargeAmount
    ChargeAmount?: number;
    // TotalChargeAmount
    TotalChargeAmount?: number;
    // LimitAmount
    LimitAmount?: number;
}
