import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CloseIcon from '@mui/icons-material/Close';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { Checkbox, Divider, Paper, Typography } from '@mui/material';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Button from '../../components/Button/Button';
import CommonDialog, { CommonDialogRef } from '../../components/CommonDialog/CommonDialog';
import Header from '../../components/Header/Header';
import Icons from '../../components/Icons';
import Stepper from '../../components/Stepper';
import Screens from '../../constants/screens';
import Utils from '../../utils/utils';
import Regulation from '../Regulation';
import { RegulationType } from '../Regulation/Regulation';
import './styles.scss';

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    maxWidth: 380,
    width: '95%',
    height: 250,
    bgcolor: 'background.paper',
    p: 3,
    borderRadius: 3,
};

// ApplicationI type
interface ApplicationI {
    fullName: string;
    fugirana: string;
    postcode: string;
    prefectures: string;
    municipalities: string;
    city: string;
    address: string;
    buildingName: string;
    contact: string;
    mailAddress: string;
}

/**
 * ApplicationConfirm
 * ID: ENTRYFORM003
 * Name: 申込フォーム (申込者情報確認画面)
 * @returns React.JSX.Element
 */
const ApplicationConfirm = (): React.JSX.Element => {
    // variable navigate
    const navigate = useNavigate();
    const [isChecked, setIsChecked] = useState(false);
    const dialogRef = useRef<CommonDialogRef>(null);
    const [type, setType] = useState<RegulationType | null>(null);
    const [open, setOpen] = useState<boolean>(false);

    // state
    const { state } = useLocation();
    // variable paramLocation
    const paramLocation = useMemo(() => {
        return state as {
            values: ApplicationI;
        };
    }, [state]);

    /**
     * Handle submit  form
     */
    const handleComplete = useCallback(() => {
        dialogRef?.current?.toggleDialog?.();
    }, []);

    /**
     * Handle close modal
     */
    const handleCloseModal = useCallback(() => {
        dialogRef?.current?.toggleDialog?.(false);
        navigate(Screens.WALLET001);
    }, [navigate]);

    // handle close dialog
    const handleClose = useCallback(() => {
        setOpen(false);
    }, []);

    /**
     * handle checked
     * @params React.ChangeEvent<HTMLInputElement>
     */
    const handleChecked = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setIsChecked(e?.target?.checked);
    }, []);

    // handle open dialog
    const handleClickOpen = useCallback(
        (type: RegulationType) => () => {
            setType(type);
            setOpen(true);
        },
        []
    );

    /**
     * handleCancel
     */
    const handleBack = useCallback(() => {
        navigate(Screens.APPLICATION_INPUT, {
            state: {
                values: paramLocation?.values,
            },
        });
    }, [navigate, paramLocation?.values]);

    return useMemo(
        () => (
            <div className="confirm-container">
                <Header
                    title={Utils.t('application_form.title.header_text')}
                    type="backLeftWithTitle"
                    onLeftButtonClick={handleBack}
                />
                <div
                    style={{
                        paddingTop: 50,
                    }}
                >
                    <div className="mt-3">
                        <Stepper step={3} configs={Utils.t('application_form.step')} disablePreviousStep />
                    </div>
                    <div
                        style={{
                            padding: 16,
                        }}
                    >
                        <Typography className="text-center text-title">
                            {Utils.t('application_form.title.comfirm_text')}
                        </Typography>
                        <Divider className="my-3 line-divider" />
                        <div className="py-1">
                            <p className="m-0 text-title-small"> {Utils.t('application_form.label.full_name')}</p>
                            <Typography className="text-detail">{paramLocation?.values?.fullName}</Typography>
                        </div>
                        <Divider className="my-2 line-divider" />
                        <div className="py-1">
                            <p className="m-0 text-title-small">{Utils.t('application_form.label.fugirana')}</p>
                            <Typography className="text-detail">{paramLocation?.values?.fugirana}</Typography>
                        </div>
                        <Divider className="my-2 line-divider" />
                        <div className="py-1">
                            <p className="m-0 text-title-small">{Utils.t('application_form.label.address')}</p>
                            <Typography className="text-detail">{paramLocation?.values?.address}</Typography>
                            <Typography className="text-detail">
                                {`${paramLocation?.values?.prefectures}
                                ${paramLocation?.values?.municipalities}
                                ${paramLocation?.values?.city}
                                ${paramLocation?.values?.buildingName}`}
                            </Typography>
                        </div>
                        <Divider className="my-2 line-divider" />
                        <div className="py-1">
                            <p className="m-0 text-title-small">{Utils.t('application_form.label.contact')}</p>
                            <Typography className="text-detail ff-roboto fs-18">
                                {paramLocation?.values?.contact}
                            </Typography>
                        </div>
                        <Divider className="my-2 line-divider" />
                        <div className="py-1">
                            <p className="m-0 text-title-small">{Utils.t('application_form.label.mailAddress')}</p>
                            <Typography className="text-detail fs-18">{paramLocation?.values?.mailAddress}</Typography>
                        </div>
                        <Divider className="my-2 line-divider" />
                        <div className="mt-3 pay-information-box">
                            <div className="information-go" onClick={handleClickOpen(RegulationType.TERMS_OF_SERVICE)}>
                                <Typography>{Utils.t('application_form.title.regulation_text')}</Typography>
                                <div className="information-icon">
                                    <KeyboardArrowRightIcon />
                                </div>
                            </div>
                            <div className="mt-3 information-checkbox">
                                <Checkbox
                                    checked={isChecked}
                                    onChange={handleChecked}
                                    color="default"
                                    icon={<Icons.UncheckedIcon color="#FFFFFF" />}
                                    checkedIcon={<Icons.CheckedIcon />}
                                />
                                <Typography>{Utils.t('application_form.title.checkbox')}</Typography>
                            </div>
                        </div>
                        <div className="my-4 note-warning">
                            <div className="note-header">
                                <Icons.UnreadNotificationIcon width={14} height={14} />{' '}
                                <p>{Utils.t('application_form.title.warning_text')}</p>
                            </div>
                            <Typography>
                                {Utils.t('application_form.warning_content', paramLocation?.values?.mailAddress)}
                            </Typography>
                        </div>
                        <div className="d-flex flex-column">
                            <Button
                                variant="contained"
                                fontWeight="bold"
                                fontSize={16}
                                height={44}
                                flex={1}
                                borderRadius={8}
                                marginBottom={8}
                                disabled={!isChecked}
                                onClick={handleComplete}
                            >
                                {Utils.t('application_form.button.application')}
                            </Button>
                            <Button
                                variant="outlined"
                                fontWeight="bold"
                                fontSize={16}
                                height={44}
                                flex={1}
                                borderRadius={8}
                                onClick={handleBack}
                            >
                                {Utils.t('application_form.button.back')}
                            </Button>
                        </div>
                    </div>
                </div>
                <CommonDialog ref={dialogRef} xCenter yCenter useBackdropDismiss useDialogCloseButton>
                    <div>
                        <div className="close-modal-icon" onClick={handleCloseModal}>
                            <CloseIcon style={{ fill: 'white', fontSize: 40 }} />
                        </div>
                        <Paper sx={style}>
                            <div className="box-modal">
                                <CheckCircleIcon style={{ fill: '#2EC147', fontSize: 70 }} />
                                <Typography className="fw-bold fs-20">
                                    {Utils.t('application_form.title.applicated')}
                                </Typography>
                                <Button
                                    variant="outlined"
                                    fontWeight="bold"
                                    fontSize={16}
                                    onClick={handleCloseModal}
                                    className="w-100"
                                >
                                    {Utils.t('application_form.button.close')}
                                </Button>
                            </div>
                        </Paper>
                    </div>
                </CommonDialog>
                <Regulation open={open} closeDialog={handleClose} regulationType={type} />
            </div>
        ),
        [
            handleBack,
            handleChecked,
            handleClickOpen,
            handleClose,
            handleCloseModal,
            handleComplete,
            isChecked,
            open,
            paramLocation?.values?.address,
            paramLocation?.values?.buildingName,
            paramLocation?.values?.city,
            paramLocation?.values?.contact,
            paramLocation?.values?.fugirana,
            paramLocation?.values?.fullName,
            paramLocation?.values?.mailAddress,
            paramLocation?.values?.municipalities,
            paramLocation?.values?.prefectures,
            type,
        ]
    );
};

export default ApplicationConfirm;
