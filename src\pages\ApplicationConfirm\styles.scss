.confirm-container {
    .text-title {
        font-size: 16px;
        font-family: "Noto Sans JP Bold", Mei<PERSON>, メイリオ, Arial, Helvetica, sans-serif;
    }
    .text-title-small {
        font-size: 12px;
        color: #707070;
    }

    .line-divider {
        height: 0.1em !important;
        background-color: rgba(0, 0, 0, 0.1) !important;
    }

    .pay-information-box {
        padding: 25px 16px 16px 16px;
        background-color: #EEEEEE;

        .information-go {
            display: flex;
            align-items: center;
            border: solid #CCCCCC;
            border-radius: 8px;
            background-color: white;
            padding: 10px;
            cursor: pointer;

            p {
                flex: 1;
            }

            .information-icon {
                width: 20px;
                height: 20px;
                background-color: #EEEEEE;
                border-radius: 50px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

        .information-checkbox {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .note-warning {
        padding: 16px;
        border: solid 1px #DE3523;
        background-color: #FEE4E4;
        margin-top: 15px;
        margin-bottom: 20px;

        p {
            font-size: 12px;
        }

        .note-header {
            display: flex;
            align-items: center;

            p {
                margin: 0;
                color: #DE3523;
                font-weight: bold;
                font-size: 14px;
            }

            .note-icon {
                color: white;
                width: 11px;
                height: 11px;
                font-size: 8px;
                display: flex;
                justify-content: center;
                align-items: center;
                background-color: #DE3523;
                border-radius: 50px;
            }
        }
    }
}

.box-modal {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    height: 100%;
}

.close-modal-icon {
    background-color: rgba(0, 0, 0, 0.4);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    float: right;
    cursor: pointer;
}