import React, { memo, useCallback, useMemo, useRef } from 'react';
import CommonAPI from '../../../apis/common';
import Container from '../../../components/Container';
import QRScanner from '../../../components/QRScanner';
import API from '../../../constants/api';
import Screens from '../../../constants/screens';
import useNavigate from '../../../hooks/navigate';
import { CommonTransferMainGetUserInfoModel } from '../../../models/common-transfer-main-get-userinfo';
import StorageServices from '../../../services/storage.service';
import CommonUtils from '../../../utils/common';
import Utils from '../../../utils/utils';

type Transfer006Props = {
    onClose: () => void;
};

export const Transfer006OrderId = 'Transfer006OrderId';

/**
 * ID: Transfer006
 * Name: QRコード読取（もらう）
 * 送るのQRコード読み取り画面
 * @returns React.JSX.Element
 */
const Transfer006 = (props: Transfer006Props): React.JSX.Element => {
    // navigate
    const navigate = useNavigate();

    const { onClose } = props;

    // ref
    const apiCalling = useRef<boolean>(false);

    /**
     * handleCallApiScanQrCodeSuccess
     * @params params: CommonTransferMainGetUserInfoModel
     */
    const handleCallApiScanQrCodeSuccess = useCallback(
        async (params: CommonTransferMainGetUserInfoModel) => {
            const { result, status, Message, statusCode } = await CommonAPI.commonTransferMainGetUserInfo(params);
            if (status === API.STATUS_CODE.SUCCESS && statusCode === API.HTTP_STATUS_CODE.SUCCESS) {
                await StorageServices.Local.set(Transfer006OrderId, result?.OrderID);

                navigate(Screens.TRANSFER007, {
                    keepStateInStorage: true,
                    state: {
                        CheerID: result?.CheerID,
                        FirstName: result?.FirstName,
                        LastName: result?.LastName,
                    },
                });
            } else {
                CommonUtils.showMessage({
                    message: Utils.t(Message || 'api.common.unknown_error'),
                    type: Message?.endsWith('WARNING') ? 'WARNING' : Message?.endsWith('INFO') ? 'INFO' : 'ERROR',
                    onClose: () => {
                        apiCalling.current = false;
                    },
                });
            }
        },
        [navigate]
    );

    /**
     * handleScanSuccess
     * @param data string | null
     */
    const handleScanSuccess = useCallback(
        async (data: string | null) => {
            if (data && !apiCalling.current) {
                apiCalling.current = true;
                // QR Code format is
                // QRCodeNumber + '#' + QRCodeType
                const qRCodeNumber = data?.split('#')[0];
                const qRCodeType = data?.split('#')[1];

                // check validate QR code data
                const qrCodeNumberRegex = /^[a-zA-Z0-9]{16}$/;
                if (!qrCodeNumberRegex.test(qRCodeNumber)) {
                    CommonUtils.showMessage({
                        title: Utils.t('transfer.transfer006.qr_code_invalid'),
                        type: 'ERROR',
                        onClose: () => {
                            apiCalling.current = false;
                        },
                    });
                    return;
                }

                if (Number(qRCodeType) !== 2) {
                    CommonUtils.showMessage({
                        title: Utils.t('transfer.transfer006.qr_code_not_allow'),
                        type: 'ERROR',
                        onClose: () => {
                            apiCalling.current = false;
                        },
                    });
                    return;
                }

                const params: CommonTransferMainGetUserInfoModel = {
                    QRCodeNumber: qRCodeNumber,
                    QRCodeType: Number(qRCodeType),
                    useLock: true,
                };
                await handleCallApiScanQrCodeSuccess(params);
            }
        },
        [handleCallApiScanQrCodeSuccess]
    );

    return useMemo(
        () => (
            <Container screenName="Transfer006">
                <QRScanner
                    onScan={handleScanSuccess}
                    isHiddenOnChangeMode
                    onChangeMode={onClose}
                    onClose={onClose}
                    header={{
                        title: Utils.t('transfer.transfer006.title'),
                        onRightButtonClick: onClose,
                        hideBack: true,
                    }}
                    infoText={Utils.t('transfer.transfer006.info_test')}
                />
            </Container>
        ),
        [handleScanSuccess, onClose]
    );
};

export default memo(Transfer006);
